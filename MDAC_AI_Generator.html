<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MDAC 智能表单脚本生成器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.3;
            color: #333;
            background: #f5f5f5;
            width: 320px;
            min-height: 100vh;
            overflow-x: hidden;
            font-size: 12px;
        }

        /* 🔒 锁定/解锁功能样式 */
        .field-lock {
            position: relative;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .lock-btn {
            background: none;
            border: none;
            cursor: pointer;
            font-size: 12px;
            padding: 2px 4px;
            border-radius: 3px;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 2px;
        }
        
        .lock-btn.locked {
            background: #e74c3c;
            color: white;
        }
        
        .lock-btn.unlocked {
            background: #2ecc71;
            color: white;
        }
        
        .lock-btn:hover {
            transform: scale(1.1);
        }
        
        .form-group input[disabled],
        .form-group select[disabled],
        .form-group textarea[disabled] {
            background: #f8f9fa;
            color: #6c757d;
            cursor: not-allowed;
            border-color: #dee2e6;
        }
        
        .form-group input[readonly],
        .form-group select[readonly] {
            background: #f8f9fa;
            color: #495057;
            border-color: #dee2e6;
        }
        
        .field-controls {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 4px;
        }
        
        .field-label {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 11px;
            font-weight: 600;
        }
        
        .lock-indicator {
            font-size: 10px;
            opacity: 0.7;
        }

        .container {
            width: 100%;
            background: #fff;
            border-radius: 0;
            box-shadow: none;
            overflow: hidden;
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 8px 10px;
            text-align: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header h1 {
            font-size: 1.2em;
            margin-bottom: 2px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 0.8em;
            opacity: 0.9;
        }

        .main-content {
            display: block;
            min-height: auto;
        }

        .input-section {
            padding: 8px;
            background: #fff;
            border-right: none;
            border-bottom: 1px solid #e9ecef;
        }

        .output-section {
            display: none; /* 隐藏原来的输出区域 */
        }

        .section-title {
            font-size: 1.3em;
            color: #2c3e50;
            margin-bottom: 10px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
        }

        .form-group {
            margin-bottom: 12px;
        }

        .form-group label {
            display: block;
            margin-bottom: 4px;
            font-weight: 600;
            color: #555;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .textarea-large {
            min-height: 120px;
            resize: vertical;
            font-family: 'Courier New', monospace;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            width: 100%;
            margin-top: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            margin-top: 5px;
        }

        .btn-secondary:hover {
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
        }

        .loading {
            display: none;
            text-align: center;
            padding: 10px;
            background: rgba(52, 152, 219, 0.1);
            border-radius: 4px;
            margin: 10px 0;
        }

        .loading::after {
            content: '';
            width: 30px;
            height: 30px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            display: inline-block;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .output-container {
            background: #2c3e50;
            border-radius: 4px;
            padding: 10px;
            min-height: 400px;
            position: relative;
        }

        .output-code {
            color: #ecf0f1;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            white-space: pre-wrap;
            word-wrap: break-word;
            max-height: 500px;
            overflow-y: auto;
            background: #2c3e50;
            border: 1px solid #34495e;
            border-radius: 4px;
            padding: 10px;
            width: 100%;
            box-sizing: border-box;
        }

        .editable-code {
            resize: vertical;
            min-height: 300px;
            transition: border-color 0.3s ease;
        }

        .editable-code:focus {
            border-color: #3498db;
            outline: none;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
        }

        .editable-hint {
            font-size: 0.8em;
            color: #7f8c8d;
            font-weight: normal;
        }

        .script-actions {
            display: flex;
            gap: 5px;
            margin-bottom: 8px;
            flex-wrap: wrap;
        }

        .script-actions .copy-btn {
            position: static;
            flex: none;
            min-width: auto;
            padding: 6px 10px;
            font-size: 11px;
            margin: 0;
        }

        .copy-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #3498db;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            z-index: 10;
        }

        .copy-btn:hover {
            background: #2980b9;
        }

        .api-key-section {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 10px;
        }

        .api-key-section h3 {
            color: #2e7d32;
            margin-bottom: 5px;
        }

        .status {
            padding: 8px;
            border-radius: 4px;
            margin: 5px 0;
            text-align: center;
            font-weight: 500;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .input-section {
                border-right: none;
                border-bottom: 1px solid #e9ecef;
            }
        }



        .quick-fill {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 5px;
            margin-bottom: 10px;
        }

        .quick-btn {
            padding: 6px 10px;
            background: #17a2b8;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
        }

        .quick-btn:hover {
            background: #138496;
        }

        .usage-guide {
            background: #e8f5e8;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
        }

        .usage-guide h3 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .usage-guide ol {
            color: #555;
            line-height: 1.8;
            padding-left: 20px;
        }

        .model-info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 15px;
            font-size: 12px;
            text-align: center;
        }

        /* 表单网格布局 */
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-column {
            background: #ffffff;
            padding: 12px;
            border-radius: 6px;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .column-title {
            color: #2c3e50;
            font-size: 1.1em;
            margin-bottom: 8px;
            padding-bottom: 4px;
            border-bottom: 2px solid #3498db;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        /* 持久化联系信息区域 */
        .persistent-section {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 12px;
            border: 2px solid #ff6b6b;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.2);
        }

        .persistent-section .column-title {
            color: #721c24;
            border-bottom-color: #ff6b6b;
        }

        .contact-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        /* 手机号码输入样式 */
        .phone-input {
            display: flex;
            gap: 5px;
        }

        .region-select {
            flex: 0 0 140px;
            padding: 8px;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            font-size: 14px;
            background: white;
        }

        .phone-number {
            flex: 1;
            padding: 8px;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            font-size: 14px;
        }

        /* AI输入区域样式 */
        .ai-input-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 15px;
            color: white;
            position: relative;
        }

        .ai-input-section label {
            color: white !important;
            font-size: 1em;
            margin-bottom: 6px;
        }

        .ai-input-section textarea {
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid transparent;
            color: #333;
            transition: border-color 0.3s ease;
        }

        .ai-input-section textarea:focus {
            border-color: #ffd700;
            box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.2);
        }

        .ai-input-section textarea.auto-parse-active {
            border-color: #ffd700;
            box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.3);
            animation: autoParsePulse 1s ease-in-out;
        }

        @keyframes autoParsePulse {
            0% { box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.3); }
            50% { box-shadow: 0 0 0 4px rgba(255, 215, 0, 0.6); }
            100% { box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.3); }
        }

        .ai-input-section .btn {
            background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
            color: #333;
            font-weight: bold;
            margin-top: 8px;
        }

        .ai-input-section .btn:hover {
            box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
            transform: translateY(-1px);
        }

        .auto-parse-indicator {
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(255, 215, 0, 0.9);
            color: #333;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: bold;
            display: none;
            animation: fadeInOut 1s ease-in-out;
        }

        @keyframes fadeInOut {
            0% { opacity: 0; transform: scale(0.8); }
            50% { opacity: 1; transform: scale(1.1); }
            100% { opacity: 1; transform: scale(1); }
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .form-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .contact-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }

        @media (max-width: 768px) {
            .phone-input {
                flex-direction: column;
                gap: 10px;
            }
            
            .region-select {
                flex: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 MDAC 脚本生成器</h1>
            <p>智能生成马来西亚数字入境卡自动填充脚本</p>
        </div>

        <div class="main-content">
            <div class="input-section">
                <h2 class="section-title">📝 旅客信息输入</h2>

                <div class="api-key-section" style="display: none;">
                    <h3>🔑 Gemini API 设置</h3>
                    <div class="model-info">
                        🤖 使用模型: gemini-2.5-flash-lite-preview-06-17
                    </div>
                    <div class="form-group">
                        <input type="password" id="apiKey" placeholder="请输入您的 Gemini API Key" value="AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s">
                        <small style="color: #2e7d32; display: block; margin-top: 5px;">
                            ✅ API Key 已预设，可直接使用 | 获取新Key: <a href="https://aistudio.google.com/app/apikey" target="_blank" rel="noopener">Google AI Studio</a>
                        </small>
                    </div>
                </div>

                <div class="quick-fill">
                    <button class="quick-btn" onclick="fillSampleData()">📋 填入示例</button>
                    <button class="quick-btn" onclick="clearForm()">🗑️ 清空</button>
                </div>

                <form id="travelerForm">
                    <!-- AI输入区域 -->
                    <div class="ai-input-section">
                        <div class="auto-parse-indicator" id="autoParseIndicator">⚡ 1秒后自动解析</div>
                        <div class="form-group">
                            <label for="travelerInfo">🤖 智能输入 (自动解析)</label>
                            <textarea 
                                id="travelerInfo" 
                                class="textarea-large" 
                                placeholder="⚡ 智能自动解析功能已启用！

只需输入超过10个字符，系统将在1秒后自动使用Gemini AI解析并填充表单！

支持格式：
1. 自然语言：
李明，中国护照G12345678，1990年1月1日出生，男性，护照2026年1月1日到期。邮箱：<EMAIL>，手机：+60123456789。计划2025年8月1日到达马来西亚，8月7日离开。乘坐MH123航班，住宿地址：吉隆坡市中心酒店，邮编50000

2. 结构化数据：
name: LI MING
passport: G12345678  
birth: 1990-01-01
nationality: CHN
gender: 男
email: <EMAIL>
phone: +60123456789
arrival: 2025-08-01
departure: 2025-08-07
flight: MH123
address: Hotel KL City Center
postcode: 50000

💡 提示：输入内容后稍等1秒，AI会自动解析！"></textarea>
                        </div>
                        
                        <button type="button" class="btn" id="aiParseBtn">
                            ⚡ 立即解析
                        </button>
                    </div>

                    <!-- 游客信息和行程信息分两列 -->
                    <div class="form-grid">
                        <div class="form-column">
                            <h3 class="column-title">👤 游客信息</h3>
                            
                            <div class="form-group">
                                <label for="passengerName">姓名 (Name)</label>
                                <input type="text" id="passengerName" placeholder="LI MING">
                            </div>
                            
                            <div class="form-group">
                                <label for="passportNo">护照号码 (Passport No.)</label>
                                <input type="text" id="passportNo" placeholder="G12345678">
                            </div>
                            
                            <div class="form-group">
                                <label for="birthDate">出生日期 (Date of Birth)</label>
                                <input type="text" id="birthDate" placeholder="01/01/1990 (DD/MM/YYYY)">
                            </div>
                            
                            <div class="form-group">
                                <label for="nationality">国籍 (Nationality)</label>
                                <select id="nationality">
                                    <option value="">请选择国籍</option>
                                    <option value="AFG">AFG - 阿富汗</option>
                                    <option value="ALB">ALB - 阿尔巴尼亚</option>
                                    <option value="DZA">DZA - 阿尔及利亚</option>
                                    <option value="AND">AND - 安道尔</option>
                                    <option value="AGO">AGO - 安哥拉</option>
                                    <option value="ARG">ARG - 阿根廷</option>
                                    <option value="ARM">ARM - 亚美尼亚</option>
                                    <option value="AUS">AUS - 澳大利亚</option>
                                    <option value="AUT">AUT - 奥地利</option>
                                    <option value="AZE">AZE - 阿塞拜疆</option>
                                    <option value="BHS">BHS - 巴哈马</option>
                                    <option value="BHR">BHR - 巴林</option>
                                    <option value="BGD">BGD - 孟加拉国</option>
                                    <option value="BRB">BRB - 巴巴多斯</option>
                                    <option value="BLR">BLR - 白俄罗斯</option>
                                    <option value="BEL">BEL - 比利时</option>
                                    <option value="BLZ">BLZ - 伯利兹</option>
                                    <option value="BEN">BEN - 贝宁</option>
                                    <option value="BTN">BTN - 不丹</option>
                                    <option value="BOL">BOL - 玻利维亚</option>
                                    <option value="BIH">BIH - 波黑</option>
                                    <option value="BWA">BWA - 博茨瓦纳</option>
                                    <option value="BRA">BRA - 巴西</option>
                                    <option value="BRN">BRN - 文莱</option>
                                    <option value="BGR">BGR - 保加利亚</option>
                                    <option value="BFA">BFA - 布基纳法索</option>
                                    <option value="BDI">BDI - 布隆迪</option>
                                    <option value="KHM">KHM - 柬埔寨</option>
                                    <option value="CMR">CMR - 喀麦隆</option>
                                    <option value="CAN">CAN - 加拿大</option>
                                    <option value="CHN">CHN - 中国</option>
                                    <option value="CUB">CUB - 古巴</option>
                                    <option value="CYP">CYP - 塞浦路斯</option>
                                    <option value="CZE">CZE - 捷克</option>
                                    <option value="DNK">DNK - 丹麦</option>
                                    <option value="EGY">EGY - 埃及</option>
                                    <option value="FIN">FIN - 芬兰</option>
                                    <option value="FRA">FRA - 法国</option>
                                    <option value="DEU">DEU - 德国</option>
                                    <option value="GRC">GRC - 希腊</option>
                                    <option value="HKG">HKG - 香港</option>
                                    <option value="HUN">HUN - 匈牙利</option>
                                    <option value="ISL">ISL - 冰岛</option>
                                    <option value="IND">IND - 印度</option>
                                    <option value="IDN">IDN - 印尼</option>
                                    <option value="IRN">IRN - 伊朗</option>
                                    <option value="IRQ">IRQ - 伊拉克</option>
                                    <option value="IRL">IRL - 爱尔兰</option>
                                    <option value="ISR">ISR - 以色列</option>
                                    <option value="ITA">ITA - 意大利</option>
                                    <option value="JAM">JAM - 牙买加</option>
                                    <option value="JPN">JPN - 日本</option>
                                    <option value="JOR">JOR - 约旦</option>
                                    <option value="KAZ">KAZ - 哈萨克斯坦</option>
                                    <option value="KEN">KEN - 肯尼亚</option>
                                    <option value="KOR">KOR - 韩国</option>
                                    <option value="KWT">KWT - 科威特</option>
                                    <option value="LAO">LAO - 老挝</option>
                                    <option value="LBN">LBN - 黎巴嫩</option>
                                    <option value="MAC">MAC - 澳门</option>
                                    <option value="MYS">MYS - 马来西亚</option>
                                    <option value="MDV">MDV - 马尔代夫</option>
                                    <option value="MEX">MEX - 墨西哥</option>
                                    <option value="MMR">MMR - 缅甸</option>
                                    <option value="NPL">NPL - 尼泊尔</option>
                                    <option value="NLD">NLD - 荷兰</option>
                                    <option value="NZL">NZL - 新西兰</option>
                                    <option value="NGA">NGA - 尼日利亚</option>
                                    <option value="NOR">NOR - 挪威</option>
                                    <option value="PAK">PAK - 巴基斯坦</option>
                                    <option value="PHL">PHL - 菲律宾</option>
                                    <option value="POL">POL - 波兰</option>
                                    <option value="PRT">PRT - 葡萄牙</option>
                                    <option value="QAT">QAT - 卡塔尔</option>
                                    <option value="RUS">RUS - 俄罗斯</option>
                                    <option value="SAU">SAU - 沙特阿拉伯</option>
                                    <option value="SGP">SGP - 新加坡</option>
                                    <option value="ZAF">ZAF - 南非</option>
                                    <option value="ESP">ESP - 西班牙</option>
                                    <option value="LKA">LKA - 斯里兰卡</option>
                                    <option value="SWE">SWE - 瑞典</option>
                                    <option value="CHE">CHE - 瑞士</option>
                                    <option value="TWN">TWN - 台湾</option>
                                    <option value="THA">THA - 泰国</option>
                                    <option value="TUR">TUR - 土耳其</option>
                                    <option value="ARE">ARE - 阿联酋</option>
                                    <option value="GBR">GBR - 英国</option>
                                    <option value="USA">USA - 美国</option>
                                    <option value="VNM">VNM - 越南</option>
                                    <option value="ZWE">ZWE - 津巴布韦</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="gender">性别 (Gender)</label>
                                <select id="gender">
                                    <option value="">请选择性别</option>
                                    <option value="1">男性 (Male)</option>
                                    <option value="2">女性 (Female)</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="passportExpiry">护照有效期 (Passport Expiry)</label>
                                <input type="text" id="passportExpiry" placeholder="01/01/2026 (DD/MM/YYYY)">
                            </div>
                        </div>
                        
                        <div class="form-column">
                            <h3 class="column-title">✈️ 行程信息</h3>
                            
                            <div class="form-group">
                                <label for="arrivalDate">到达日期 (Arrival Date)</label>
                                <input type="text" id="arrivalDate" placeholder="01/08/2025 (DD/MM/YYYY)">
                            </div>
                            
                            <div class="form-group">
                                <label for="departureDate">离开日期 (Departure Date)</label>
                                <input type="text" id="departureDate" placeholder="07/08/2025 (DD/MM/YYYY)">
                            </div>
                            
                            <div class="form-group">
                                <label for="flightNo">航班/交通工具号码 (Flight/Vessel No.)</label>
                                <input type="text" id="flightNo" placeholder="MH123">
                            </div>
                            
                            <div class="form-group">
                                <label for="travelMode">交通方式 (Mode of Travel)</label>
                                <select id="travelMode">
                                    <option value="">请选择交通方式</option>
                                    <option value="1">飞机 (Air)</option>
                                    <option value="2">陆路 (Land)</option>
                                    <option value="3">海运 (Sea)</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="embark">到达马来西亚前的最后登船港 (Last Port of Embarkation)</label>
                                <select id="embark">
                                    <option value="">请选择国家/地区</option>
                                    <option value="AFG">AFG - 阿富汗</option>
                                    <option value="ALB">ALB - 阿尔巴尼亚</option>
                                    <option value="DZA">DZA - 阿尔及利亚</option>
                                    <option value="AND">AND - 安道尔</option>
                                    <option value="AGO">AGO - 安哥拉</option>
                                    <option value="ARG">ARG - 阿根廷</option>
                                    <option value="ARM">ARM - 亚美尼亚</option>
                                    <option value="AUS">AUS - 澳大利亚</option>
                                    <option value="AUT">AUT - 奥地利</option>
                                    <option value="AZE">AZE - 阿塞拜疆</option>
                                    <option value="BHS">BHS - 巴哈马</option>
                                    <option value="BHR">BHR - 巴林</option>
                                    <option value="BGD">BGD - 孟加拉国</option>
                                    <option value="BRB">BRB - 巴巴多斯</option>
                                    <option value="BLR">BLR - 白俄罗斯</option>
                                    <option value="BEL">BEL - 比利时</option>
                                    <option value="BLZ">BLZ - 伯利兹</option>
                                    <option value="BEN">BEN - 贝宁</option>
                                    <option value="BTN">BTN - 不丹</option>
                                    <option value="BOL">BOL - 玻利维亚</option>
                                    <option value="BIH">BIH - 波黑</option>
                                    <option value="BWA">BWA - 博茨瓦纳</option>
                                    <option value="BRA">BRA - 巴西</option>
                                    <option value="BRN">BRN - 文莱</option>
                                    <option value="BGR">BGR - 保加利亚</option>
                                    <option value="BFA">BFA - 布基纳法索</option>
                                    <option value="BDI">BDI - 布隆迪</option>
                                    <option value="KHM">KHM - 柬埔寨</option>
                                    <option value="CMR">CMR - 喀麦隆</option>
                                    <option value="CAN">CAN - 加拿大</option>
                                    <option value="CHN">CHN - 中国</option>
                                    <option value="CUB">CUB - 古巴</option>
                                    <option value="CYP">CYP - 塞浦路斯</option>
                                    <option value="CZE">CZE - 捷克</option>
                                    <option value="DNK">DNK - 丹麦</option>
                                    <option value="EGY">EGY - 埃及</option>
                                    <option value="FIN">FIN - 芬兰</option>
                                    <option value="FRA">FRA - 法国</option>
                                    <option value="DEU">DEU - 德国</option>
                                    <option value="GRC">GRC - 希腊</option>
                                    <option value="HKG">HKG - 香港</option>
                                    <option value="HUN">HUN - 匈牙利</option>
                                    <option value="ISL">ISL - 冰岛</option>
                                    <option value="IND">IND - 印度</option>
                                    <option value="IDN">IDN - 印尼</option>
                                    <option value="IRN">IRN - 伊朗</option>
                                    <option value="IRQ">IRQ - 伊拉克</option>
                                    <option value="IRL">IRL - 爱尔兰</option>
                                    <option value="ISR">ISR - 以色列</option>
                                    <option value="ITA">ITA - 意大利</option>
                                    <option value="JAM">JAM - 牙买加</option>
                                    <option value="JPN">JPN - 日本</option>
                                    <option value="JOR">JOR - 约旦</option>
                                    <option value="KAZ">KAZ - 哈萨克斯坦</option>
                                    <option value="KEN">KEN - 肯尼亚</option>
                                    <option value="KOR">KOR - 韩国</option>
                                    <option value="KWT">KWT - 科威特</option>
                                    <option value="LAO">LAO - 老挝</option>
                                    <option value="LBN">LBN - 黎巴嫩</option>
                                    <option value="MAC">MAC - 澳门</option>
                                    <option value="MYS">MYS - 马来西亚</option>
                                    <option value="MDV">MDV - 马尔代夫</option>
                                    <option value="MEX">MEX - 墨西哥</option>
                                    <option value="MMR">MMR - 缅甸</option>
                                    <option value="NPL">NPL - 尼泊尔</option>
                                    <option value="NLD">NLD - 荷兰</option>
                                    <option value="NZL">NZL - 新西兰</option>
                                    <option value="NGA">NGA - 尼日利亚</option>
                                    <option value="NOR">NOR - 挪威</option>
                                    <option value="PAK">PAK - 巴基斯坦</option>
                                    <option value="PHL">PHL - 菲律宾</option>
                                    <option value="POL">POL - 波兰</option>
                                    <option value="PRT">PRT - 葡萄牙</option>
                                    <option value="QAT">QAT - 卡塔尔</option>
                                    <option value="RUS">RUS - 俄罗斯</option>
                                    <option value="SAU">SAU - 沙特阿拉伯</option>
                                    <option value="SGP">SGP - 新加坡</option>
                                    <option value="ZAF">ZAF - 南非</option>
                                    <option value="ESP">ESP - 西班牙</option>
                                    <option value="LKA">LKA - 斯里兰卡</option>
                                    <option value="SWE">SWE - 瑞典</option>
                                    <option value="CHE">CHE - 瑞士</option>
                                    <option value="TWN">TWN - 台湾</option>
                                    <option value="THA">THA - 泰国</option>
                                    <option value="TUR">TUR - 土耳其</option>
                                    <option value="ARE">ARE - 阿联酋</option>
                                    <option value="GBR">GBR - 英国</option>
                                    <option value="USA">USA - 美国</option>
                                    <option value="VNM">VNM - 越南</option>
                                    <option value="ZWE">ZWE - 津巴布韦</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="accommodationType">住宿类型 (Accommodation Type)</label>
                                <select id="accommodationType">
                                    <option value="">请选择住宿类型</option>
                                    <option value="01">酒店/旅馆 (Hotel/Motel)</option>
                                    <option value="02">亲友住所 (Friends/Relatives)</option>
                                    <option value="99">其他 (Others)</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="address1">住宿地址 (Address Line 1)</label>
                                <input type="text" id="address1" placeholder="Hotel KL City Center">
                            </div>
                            
                            <div class="form-group">
                                <label for="address2">地址第二行 (Address Line 2)</label>
                                <input type="text" id="address2" placeholder="可选 (Optional)">
                            </div>
                            
                            <div class="form-group">
                                <label for="state">州属 (State)</label>
                                <select id="state">
                                    <option value="">请选择州属</option>
                                    <option value="01">柔佛 (Johor)</option>
                                    <option value="02">吉打 (Kedah)</option>
                                    <option value="03">吉兰丹 (Kelantan)</option>
                                    <option value="04">马六甲 (Melaka)</option>
                                    <option value="05">森美兰 (Negeri Sembilan)</option>
                                    <option value="06">彭亨 (Pahang)</option>
                                    <option value="07">槟城 (Pulau Pinang)</option>
                                    <option value="08">霹雳 (Perak)</option>
                                    <option value="09">玻璃市 (Perlis)</option>
                                    <option value="10">雪兰莪 (Selangor)</option>
                                    <option value="11">登嘉楼 (Terengganu)</option>
                                    <option value="12">沙巴 (Sabah)</option>
                                    <option value="13">砂拉越 (Sarawak)</option>
                                    <option value="14">吉隆坡 (WP Kuala Lumpur)</option>
                                    <option value="15">纳闽 (WP Labuan)</option>
                                    <option value="16">布城 (WP Putrajaya)</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="postcode">邮政编码 (Postcode)</label>
                                <input type="text" id="postcode" placeholder="50000">
                            </div>
                        </div>
                    </div>

                    <!-- 底部持久化联系信息 -->
                    <div class="persistent-section">
                        <h3 class="column-title">📞 联系信息 (持久化)</h3>
                        <div class="contact-grid">
                            <div class="form-group">
                                <label for="persistentEmail">电子邮箱 (Email)</label>
                                <input type="email" id="persistentEmail" placeholder="<EMAIL>">
                            </div>
                            
                            <div class="form-group">
                                <label for="persistentPhone">手机号码 (Mobile)</label>
                                <div class="phone-input">
                                    <select id="phoneRegion" class="region-select" title="选择国家/地区电话代码">
                                        <option value="1">1 (美国/加拿大)</option>
                                        <option value="20">20 (埃及)</option>
                                        <option value="30">30 (希腊)</option>
                                        <option value="31">31 (荷兰)</option>
                                        <option value="32">32 (比利时)</option>
                                        <option value="33">33 (法国)</option>
                                        <option value="34">34 (西班牙)</option>
                                        <option value="36">36 (匈牙利)</option>
                                        <option value="39">39 (意大利)</option>
                                        <option value="40">40 (罗马尼亚)</option>
                                        <option value="41">41 (瑞士)</option>
                                        <option value="43">43 (奥地利)</option>
                                        <option value="44">44 (英国)</option>
                                        <option value="45">45 (丹麦)</option>
                                        <option value="46">46 (瑞典)</option>
                                        <option value="47">47 (挪威)</option>
                                        <option value="48">48 (波兰)</option>
                                        <option value="49">49 (德国)</option>
                                        <option value="60" selected>60 (马来西亚)</option>
                                        <option value="61">61 (澳洲)</option>
                                        <option value="62">62 (印尼)</option>
                                        <option value="63">63 (菲律宾)</option>
                                        <option value="64">64 (新西兰)</option>
                                        <option value="65">65 (新加坡)</option>
                                        <option value="66">66 (泰国)</option>
                                        <option value="81">81 (日本)</option>
                                        <option value="82">82 (韩国)</option>
                                        <option value="84">84 (越南)</option>
                                        <option value="86">86 (中国)</option>
                                        <option value="90">90 (土耳其)</option>
                                        <option value="91">91 (印度)</option>
                                        <option value="92">92 (巴基斯坦)</option>
                                        <option value="94">94 (斯里兰卡)</option>
                                        <option value="95">95 (缅甸)</option>
                                        <option value="212">212 (摩洛哥)</option>
                                        <option value="213">213 (阿尔及利亚)</option>
                                        <option value="216">216 (突尼斯)</option>
                                        <option value="218">218 (利比亚)</option>
                                        <option value="220">220 (冈比亚)</option>
                                        <option value="221">221 (塞内加尔)</option>
                                        <option value="222">222 (毛里塔尼亚)</option>
                                        <option value="223">223 (马里)</option>
                                        <option value="224">224 (几内亚)</option>
                                        <option value="225">225 (科特迪瓦)</option>
                                        <option value="226">226 (布基纳法索)</option>
                                        <option value="227">227 (尼日尔)</option>
                                        <option value="228">228 (多哥)</option>
                                        <option value="229">229 (贝宁)</option>
                                        <option value="230">230 (毛里求斯)</option>
                                        <option value="231">231 (利比里亚)</option>
                                        <option value="232">232 (塞拉利昂)</option>
                                        <option value="233">233 (加纳)</option>
                                        <option value="234">234 (尼日利亚)</option>
                                        <option value="235">235 (乍得)</option>
                                        <option value="236">236 (中非共和国)</option>
                                        <option value="237">237 (喀麦隆)</option>
                                        <option value="238">238 (佛得角)</option>
                                        <option value="239">239 (圣多美和普林西比)</option>
                                        <option value="240">240 (赤道几内亚)</option>
                                        <option value="241">241 (加蓬)</option>
                                        <option value="242">242 (刚果(布))</option>
                                        <option value="243">243 (刚果(金))</option>
                                        <option value="244">244 (安哥拉)</option>
                                        <option value="245">245 (几内亚比绍)</option>
                                        <option value="246">246 (英属印度洋领地)</option>
                                        <option value="247">247 (阿森松岛)</option>
                                        <option value="248">248 (塞舌尔)</option>
                                        <option value="249">249 (苏丹)</option>
                                        <option value="250">250 (卢旺达)</option>
                                        <option value="251">251 (埃塞俄比亚)</option>
                                        <option value="252">252 (索马里)</option>
                                        <option value="253">253 (吉布提)</option>
                                        <option value="254">254 (肯尼亚)</option>
                                        <option value="255">255 (坦桑尼亚)</option>
                                        <option value="256">256 (乌干达)</option>
                                        <option value="257">257 (布隆迪)</option>
                                        <option value="258">258 (莫桑比克)</option>
                                        <option value="260">260 (赞比亚)</option>
                                        <option value="261">261 (马达加斯加)</option>
                                        <option value="262">262 (留尼汪)</option>
                                        <option value="263">263 (津巴布韦)</option>
                                        <option value="264">264 (纳米比亚)</option>
                                        <option value="265">265 (马拉维)</option>
                                        <option value="266">266 (莱索托)</option>
                                        <option value="267">267 (博茨瓦纳)</option>
                                        <option value="268">268 (斯威士兰)</option>
                                        <option value="269">269 (科摩罗)</option>
                                        <option value="290">290 (圣赫勒拿)</option>
                                        <option value="291">291 (厄立特里亚)</option>
                                        <option value="297">297 (阿鲁巴)</option>
                                        <option value="298">298 (法罗群岛)</option>
                                        <option value="299">299 (格陵兰)</option>
                                        <option value="350">350 (直布罗陀)</option>
                                        <option value="351">351 (葡萄牙)</option>
                                        <option value="352">352 (卢森堡)</option>
                                        <option value="353">353 (爱尔兰)</option>
                                        <option value="354">354 (冰岛)</option>
                                        <option value="355">355 (阿尔巴尼亚)</option>
                                        <option value="356">356 (马耳他)</option>
                                        <option value="357">357 (塞浦路斯)</option>
                                        <option value="358">358 (芬兰)</option>
                                        <option value="359">359 (保加利亚)</option>
                                        <option value="370">370 (立陶宛)</option>
                                        <option value="371">371 (拉脱维亚)</option>
                                        <option value="372">372 (爱沙尼亚)</option>
                                        <option value="373">373 (摩尔多瓦)</option>
                                        <option value="374">374 (亚美尼亚)</option>
                                        <option value="375">375 (白俄罗斯)</option>
                                        <option value="376">376 (安道尔)</option>
                                        <option value="377">377 (摩纳哥)</option>
                                        <option value="378">378 (圣马力诺)</option>
                                        <option value="380">380 (乌克兰)</option>
                                        <option value="381">381 (塞尔维亚)</option>
                                        <option value="382">382 (黑山)</option>
                                        <option value="383">383 (科索沃)</option>
                                        <option value="385">385 (克罗地亚)</option>
                                        <option value="386">386 (斯洛文尼亚)</option>
                                        <option value="387">387 (波黑)</option>
                                        <option value="389">389 (北马其顿)</option>
                                        <option value="420">420 (捷克)</option>
                                        <option value="421">421 (斯洛伐克)</option>
                                        <option value="423">423 (列支敦士登)</option>
                                        <option value="500">500 (福克兰群岛)</option>
                                        <option value="501">501 (伯利兹)</option>
                                        <option value="502">502 (危地马拉)</option>
                                        <option value="503">503 (萨尔瓦多)</option>
                                        <option value="504">504 (洪都拉斯)</option>
                                        <option value="505">505 (尼加拉瓜)</option>
                                        <option value="506">506 (哥斯达黎加)</option>
                                        <option value="507">507 (巴拿马)</option>
                                        <option value="508">508 (圣皮埃尔和密克隆)</option>
                                        <option value="509">509 (海地)</option>
                                        <option value="590">590 (瓜德罗普)</option>
                                        <option value="591">591 (玻利维亚)</option>
                                        <option value="592">592 (圭亚那)</option>
                                        <option value="593">593 (厄瓜多尔)</option>
                                        <option value="594">594 (法属圭亚那)</option>
                                        <option value="595">595 (巴拉圭)</option>
                                        <option value="596">596 (马提尼克)</option>
                                        <option value="597">597 (苏里南)</option>
                                        <option value="598">598 (乌拉圭)</option>
                                        <option value="599">599 (荷属安的列斯)</option>
                                        <option value="670">670 (东帝汶)</option>
                                        <option value="672">672 (澳大利亚外岛)</option>
                                        <option value="673">673 (文莱)</option>
                                        <option value="674">674 (瑙鲁)</option>
                                        <option value="675">675 (巴布亚新几内亚)</option>
                                        <option value="676">676 (汤加)</option>
                                        <option value="677">677 (所罗门群岛)</option>
                                        <option value="678">678 (瓦努阿图)</option>
                                        <option value="679">679 (斐济)</option>
                                        <option value="680">680 (帕劳)</option>
                                        <option value="681">681 (瓦利斯和富图纳)</option>
                                        <option value="682">682 (库克群岛)</option>
                                        <option value="683">683 (纽埃)</option>
                                        <option value="684">684 (美属萨摩亚)</option>
                                        <option value="685">685 (萨摩亚)</option>
                                        <option value="686">686 (基里巴斯)</option>
                                        <option value="687">687 (新喀里多尼亚)</option>
                                        <option value="688">688 (图瓦卢)</option>
                                        <option value="689">689 (法属波利尼西亚)</option>
                                        <option value="690">690 (托克劳)</option>
                                        <option value="691">691 (密克罗尼西亚)</option>
                                        <option value="692">692 (马绍尔群岛)</option>
                                        <option value="850">850 (朝鲜)</option>
                                        <option value="852">852 (香港)</option>
                                        <option value="853">853 (澳门)</option>
                                        <option value="855">855 (柬埔寨)</option>
                                        <option value="856">856 (老挝)</option>
                                        <option value="880">880 (孟加拉国)</option>
                                        <option value="886">886 (台湾)</option>
                                        <option value="960">960 (马尔代夫)</option>
                                        <option value="961">961 (黎巴嫩)</option>
                                        <option value="962">962 (约旦)</option>
                                        <option value="963">963 (叙利亚)</option>
                                        <option value="964">964 (伊拉克)</option>
                                        <option value="965">965 (科威特)</option>
                                        <option value="966">966 (沙特阿拉伯)</option>
                                        <option value="967">967 (也门)</option>
                                        <option value="968">968 (阿曼)</option>
                                        <option value="970">970 (巴勒斯坦)</option>
                                        <option value="971">971 (阿联酋)</option>
                                        <option value="972">972 (以色列)</option>
                                        <option value="973">973 (巴林)</option>
                                        <option value="974">974 (卡塔尔)</option>
                                        <option value="975">975 (不丹)</option>
                                        <option value="976">976 (蒙古)</option>
                                        <option value="977">977 (尼泊尔)</option>
                                        <option value="98">98 (伊朗)</option>
                                        <option value="993">993 (土库曼斯坦)</option>
                                        <option value="994">994 (阿塞拜疆)</option>
                                        <option value="995">995 (格鲁吉亚)</option>
                                        <option value="996">996 (吉尔吉斯斯坦)</option>
                                        <option value="998">998 (乌兹别克斯坦)</option>
                                    </select>
                                    <input type="text" id="persistentPhone" placeholder="123456789" class="phone-number">
                                </div>
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="btn" id="generateBtn">
                        🤖 生成脚本
                    </button>

                    <button type="button" class="btn btn-secondary" onclick="generateBookmarkScript()">
                        🔖 生成书签
                    </button>
                </form>

                <div class="loading" id="loading">
                    AI解析中...
                </div>

                <div id="status"></div>
            </div>

            <div class="output-section">
                <h2 class="section-title">📜 生成的脚本 <span class="editable-hint">(可编辑)</span></h2>
                
                <div class="output-container">
                    <div class="script-actions">
                        <button class="copy-btn" onclick="copyScript()">📋 复制脚本</button>
                        <button class="copy-btn" onclick="beautifyScript()" title="格式化脚本，便于编辑">🎨 格式化</button>
                        <button class="copy-btn" onclick="minifyScript()" title="压缩脚本，减少体积">📦 压缩</button>
                        <button class="copy-btn" onclick="resetScript()" title="重新生成原始脚本">🔄 重置</button>
                    </div>
                    <textarea class="output-code editable-code" id="outputCode" spellcheck="false" placeholder="点击'生成脚本'按钮后，这里将显示自动填充脚本...&#10;&#10;💡 提示：脚本生成后可以直接编辑修改！" title="生成的脚本代码 - 可编辑">
// 🤖 AI生成的MDAC表单自动填充脚本
// 等待您输入旅客信息...

// 使用方法：
// 1. 输入旅客信息并点击"智能生成脚本"
// 2. 复制生成的JavaScript脚本 
// 3. 在MDAC网站页面按F12打开浏览器控制台
// 4. 粘贴脚本到控制台并按回车执行
// 5. 检查自动填充结果，确认无误后提交表单

// 支持功能：
// ✅ 所有表单字段自动填充
// ✅ 级联下拉菜单处理
// ✅ 日期格式自动转换
// ✅ 错误处理和验证
// ✅ 填充状态实时反馈

console.log('🚀 MDAC脚本生成器已就绪！');
                    </textarea>
                </div>

                <div class="usage-guide">
                    <h3>📋 使用说明</h3>
                    <ol>
                        <li>输入旅客信息（支持自然语言）</li>
                        <li>AI自动解析并填充表单</li>
                        <li>复制生成的脚本</li>
                        <li>打开 <a href="https://imigresen-online.imi.gov.my/mdac/main?registerMain" target="_blank" rel="noopener">MDAC网站</a></li>
                        <li>按F12打开控制台，粘贴脚本并执行</li>
                        <li>确认信息无误后提交</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Gemini API 配置
        const GEMINI_API_KEY_DEFAULT = 'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s';
        const GEMINI_MODEL = 'gemini-2.5-flash-lite-preview-06-17';
        const GEMINI_API_URL = `https://generativelanguage.googleapis.com/v1beta/models/${GEMINI_MODEL}:generateContent`;

        // 示例数据
        const SAMPLE_DATA = {
            passengerName: 'LI MING',
            passportNo: 'G12345678',
            birthDate: '01/01/1990',
            nationality: 'CHN',
            gender: '1',
            passportExpiry: '01/01/2026',
            arrivalDate: '01/08/2025',
            departureDate: '07/08/2025',
            flightNo: 'MH123',
            travelMode: '1',
            accommodationType: '01',
            address1: 'Hotel KL City Center',
            address2: '',
            state: '14',
            postcode: '50000'
        };

        // 填入示例数据
        function fillSampleData() {
            const sampleText = `李明，中国护照G12345678，1990年1月1日出生，男性，护照2026年1月1日到期。
邮箱：<EMAIL>，手机：+60123456789
计划2025年8月1日到达马来西亚，8月7日离开
乘坐MH123航班，住宿地址：吉隆坡Hotel KL City Center，邮编50000`;
            
            document.getElementById('travelerInfo').value = sampleText;
            showStatus('✅ 示例数据已填入', 'success');
        }

        // 清空表单
        function clearForm() {
            // 清空AI输入框
            document.getElementById('travelerInfo').value = '';
            
            // 清空结构化表单字段
            const formFields = [
                'passengerName', 'passportNo', 'birthDate', 'nationality', 'gender', 'passportExpiry',
                'arrivalDate', 'departureDate', 'flightNo', 'travelMode', 'embark', 'accommodationType',
                'address1', 'address2', 'state', 'postcode'
            ];
            
            formFields.forEach(fieldId => {
                const element = document.getElementById(fieldId);
                if (element) {
                    element.value = '';
                }
            });
            showStatus('🗑️ 表单已清空', 'info');
        }

        // 从表单收集数据
        function collectFormData() {
            // 始终优先使用界面当前值，确保用户的选择能正确反映
            const persistentEmail = document.getElementById('persistentEmail').value.trim() || localStorage.getItem('persistent_email') || '';
            const persistentPhone = document.getElementById('persistentPhone').value.trim() || localStorage.getItem('persistent_phone') || '';
            const phoneRegion = document.getElementById('phoneRegion').value || localStorage.getItem('phone_region') || '60';
            
            return {
                name: document.getElementById('passengerName').value.trim(),
                passNo: document.getElementById('passportNo').value.trim(),
                dob: document.getElementById('birthDate').value.trim(),
                nationality: document.getElementById('nationality').value,
                sex: document.getElementById('gender').value,
                passExpDte: document.getElementById('passportExpiry').value.trim(),
                email: persistentEmail,
                confirmEmail: persistentEmail,
                region: phoneRegion || '60',
                mobile: persistentPhone,
                arrDt: document.getElementById('arrivalDate').value.trim(),
                depDt: document.getElementById('departureDate').value.trim(),
                vesselNm: document.getElementById('flightNo').value.trim(),
                trvlMode: document.getElementById('travelMode').value || '1',
                embark: document.getElementById('embark').value || document.getElementById('nationality').value, // 优先使用embark字段，fallback到nationality
                accommodationStay: document.getElementById('accommodationType').value || '01',
                accommodationAddress1: document.getElementById('address1').value.trim(),
                accommodationAddress2: document.getElementById('address2').value.trim(),
                accommodationState: document.getElementById('state').value,
                accommodationCity: '', // 由脚本中的智能选择处理
                accommodationPostcode: document.getElementById('postcode').value.trim()
            };
        }

        // 验证表单数据
        function validateFormData(data) {
            const requiredFields = [
                { key: 'name', label: '姓名' },
                { key: 'passNo', label: '护照号码' },
                { key: 'dob', label: '出生日期' },
                { key: 'nationality', label: '国籍' },
                { key: 'sex', label: '性别' },
                { key: 'email', label: '电子邮箱' },
                { key: 'mobile', label: '手机号码' },
                { key: 'arrDt', label: '到达日期' },
                { key: 'depDt', label: '离开日期' },
                { key: 'accommodationAddress1', label: '住宿地址' }
            ];

            for (const field of requiredFields) {
                if (!data[field.key] || data[field.key] === '') {
                    throw new Error(`请填写 ${field.label}`);
                }
            }

            // 日期格式验证
            const datePattern = /^\d{2}\/\d{2}\/\d{4}$/;
            if (data.dob && !datePattern.test(data.dob)) {
                throw new Error('出生日期格式应为 DD/MM/YYYY');
            }
            if (data.passExpDte && !datePattern.test(data.passExpDte)) {
                throw new Error('护照有效期格式应为 DD/MM/YYYY');
            }
            if (data.arrDt && !datePattern.test(data.arrDt)) {
                throw new Error('到达日期格式应为 DD/MM/YYYY');
            }
            if (data.depDt && !datePattern.test(data.depDt)) {
                throw new Error('离开日期格式应为 DD/MM/YYYY');
            }

            return true;
        }

        // 保存持久化数据
        function savePersistentData() {
            const email = document.getElementById('persistentEmail').value.trim();
            const phone = document.getElementById('persistentPhone').value.trim();
            const region = document.getElementById('phoneRegion').value;

            // 始终保存当前界面值，确保localStorage与界面同步
            localStorage.setItem('persistent_email', email);
            localStorage.setItem('persistent_phone', phone);
            localStorage.setItem('phone_region', region);
            
            console.log('📝 保存持久化数据:', { email, phone, region });
        }

        // 加载持久化数据
        function loadPersistentData() {
            const savedEmail = localStorage.getItem('persistent_email');
            const savedPhone = localStorage.getItem('persistent_phone');
            const savedRegion = localStorage.getItem('phone_region');

            if (savedEmail) {
                document.getElementById('persistentEmail').value = savedEmail;
            }
            if (savedPhone) {
                document.getElementById('persistentPhone').value = savedPhone;
            }
            if (savedRegion) {
                document.getElementById('phoneRegion').value = savedRegion;
            }
        }

        // 字段ID映射函数 - 处理脚本字段ID与实际网站字段ID的差异
        function getActualFieldId(scriptFieldId) {
            const fieldMapping = {
                'phoneRegion': 'region',  // 脚本中使用phoneRegion，实际网站使用region
                'region': 'region'        // 保持一致性
            };
            return fieldMapping[scriptFieldId] || scriptFieldId;
        }

        // 填充表单字段
        function fillFormFields(data) {
            const fieldMapping = {
                'name': 'passengerName',
                'passNo': 'passportNo',
                'dob': 'birthDate',
                'nationality': 'nationality',
                'sex': 'gender',
                'passExpDte': 'passportExpiry',
                'arrDt': 'arrivalDate',
                'depDt': 'departureDate',
                'vesselNm': 'flightNo',
                'trvlMode': 'travelMode',
                'embark': 'embark',
                'accommodationStay': 'accommodationType',
                'accommodationAddress1': 'address1',
                'accommodationAddress2': 'address2',
                'accommodationState': 'state',
                'accommodationPostcode': 'postcode'
            };

            Object.keys(fieldMapping).forEach(dataKey => {
                const formFieldId = fieldMapping[dataKey];
                const element = document.getElementById(formFieldId);
                
                if (element && data[dataKey] !== undefined && data[dataKey] !== null && data[dataKey] !== '') {
                    element.value = data[dataKey];
                    // 触发change事件以确保下拉菜单更新
                    element.dispatchEvent(new Event('change', { bubbles: true }));
                }
            });

            // 填充持久化联系信息到专门的区域
            if (data.email) {
                document.getElementById('persistentEmail').value = data.email;
            }
            if (data.mobile) {
                document.getElementById('persistentPhone').value = data.mobile;
            }
            if (data.region) {
                document.getElementById('phoneRegion').value = data.region;
            }
            
            // 保存持久化数据
            savePersistentData();
            
            console.log('表单字段已填充:', data);
        }

        // 🎯 选择性填充表单字段（用于AI解析结果）
        function fillFormFieldsSelectively(data) {
            console.log('🔍 开始选择性填充表单字段...');
            console.log('📊 AI解析数据:', data);
            
            // 生成器页面的字段映射
            const fieldMapping = {
                'name': 'passengerName',
                'passNo': 'passportNo',
                'dob': 'birthDate',
                'nationality': 'nationality',
                'sex': 'gender',
                'passExpDte': 'passportExpiry',
                'email': 'email',
                'confirmEmail': 'confirmEmail',
                'region': 'region',
                'mobile': 'phoneNumber',
                'arrDt': 'arrivalDate',
                'depDt': 'departureDate',
                'vesselNm': 'flightNo',
                'trvlMode': 'travelMode',
                'embark': 'embark',
                'accommodationStay': 'accommodationType',
                'accommodationAddress1': 'address1',
                'accommodationAddress2': 'address2',
                'accommodationState': 'state',
                'accommodationPostcode': 'postcode'
            };
            
            let updateCount = 0;
            let skipCount = 0;
            
            Object.keys(fieldMapping).forEach(dataKey => {
                const formFieldId = fieldMapping[dataKey];
                const element = document.getElementById(formFieldId);
                const value = data[dataKey];
                
                // 选择性更新：只更新非空值
                if (value === null || value === undefined || value === '') {
                    console.log(`⏸️ 跳过空字段 ${dataKey} → ${formFieldId}`);
                    skipCount++;
                    return;
                }
                
                if (element) {
                    const oldValue = element.value;
                    element.value = value;
                    
                    // 触发change事件以确保下拉菜单更新
                    element.dispatchEvent(new Event('change', { bubbles: true }));
                    
                    console.log(`✅ 更新字段 ${dataKey} → ${formFieldId}: "${oldValue}" → "${value}"`);
                    updateCount++;
                } else {
                    console.warn(`⚠️ 字段元素未找到: ${formFieldId}`);
                }
            });
            
            // 同时更新持久化联系信息（如果有值）
            if (data.email && data.email !== '') {
                const persistentEmail = document.getElementById('persistentEmail');
                if (persistentEmail) {
                    persistentEmail.value = data.email;
                    console.log(`✅ 更新持久化邮箱: ${data.email}`);
                }
            }
            
            if (data.mobile && data.mobile !== '') {
                const persistentPhone = document.getElementById('persistentPhone');
                if (persistentPhone) {
                    persistentPhone.value = data.mobile;
                    console.log(`✅ 更新持久化手机: ${data.mobile}`);
                }
            }
            
            if (data.region && data.region !== '') {
                const phoneRegion = document.getElementById('phoneRegion');
                if (phoneRegion) {
                    phoneRegion.value = data.region;
                    console.log(`✅ 更新地区代码: ${data.region}`);
                }
            }
            
            console.log(`📊 填充完成: ${updateCount} 个字段已更新, ${skipCount} 个字段已跳过`);
            
            // 如果州属字段有变化，触发城市列表更新
            if (data.accommodationState && data.accommodationState !== '') {
                const stateElement = document.getElementById('state');
                if (stateElement) {
                    console.log('🏛️ 州属字段已更新，触发城市列表刷新');
                    stateElement.dispatchEvent(new Event('change', { bubbles: true }));
                }
            }
            
            // 保存持久化数据
            savePersistentData();
        }

        // AI解析函数
        async function performAIAnalysis() {
            const aiText = document.getElementById('travelerInfo').value.trim();
            const apiKey = document.getElementById('apiKey').value.trim() || GEMINI_API_KEY_DEFAULT;
            const aiParseBtn = document.getElementById('aiParseBtn');
            
            if (!aiText || aiText.length <= 10) {
                return;
            }

            try {
                aiParseBtn.disabled = true;
                aiParseBtn.textContent = '🤖 解析中...';
                showStatus('🤖 AI解析中...', 'info');

                const extractedData = await extractDataWithGemini(aiText, apiKey);
                
                // 使用改进的选择性填充表单字段
                fillFormFieldsSelectively(extractedData);
                
                showStatus('✅ AI解析完成！请检查并确认表单数据', 'success');
                
            } catch (error) {
                console.error('AI解析失败:', error);
                showStatus(`❌ AI解析失败: ${error.message}`, 'error');
            } finally {
                aiParseBtn.disabled = false;
                aiParseBtn.textContent = '⚡ 立即解析';
            }
        }

        // AI解析按钮处理
        document.getElementById('aiParseBtn').addEventListener('click', performAIAnalysis);

        // 表单提交处理
        document.getElementById('travelerForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            try {
                // 保存持久化数据
                savePersistentData();
                
                // 收集表单数据
                const formData = collectFormData();
                
                // 验证数据
                validateFormData(formData);
                
                // 生成脚本
                await generateScriptFromFormData(formData);
                
            } catch (error) {
                showStatus(`❌ ${error.message}`, 'error');
            }
        });

        // 从表单数据生成脚本
        async function generateScriptFromFormData(formData) {
            const generateBtn = document.getElementById('generateBtn');
            const loading = document.getElementById('loading');
            
            try {
                generateBtn.disabled = true;
                loading.style.display = 'block';
                showStatus('🚀 生成脚本中...', 'info');

                const script = createMDACScript(formData);
                
                const outputCode = document.getElementById('outputCode');
                outputCode.value = script;
                
                // 保存原始脚本供重置使用
                originalScript = script;
                
                showStatus('✅ 脚本生成成功！', 'success');
                
            } catch (error) {
                console.error('生成脚本出错:', error);
                showStatus(`❌ 生成失败: ${error.message}`, 'error');
            } finally {
                generateBtn.disabled = false;
                loading.style.display = 'none';
            }
        }

        // 使用Gemini API提取数据
        async function extractDataWithGemini(travelerInfo, apiKey) {
            const prompt = `
你是一个专业的MDAC表单数据提取助手。请从以下旅客信息中提取数据，并返回严格的JSON格式。

MDAC表单字段说明：
- name: 姓名（大写英文）
- passNo: 护照号码  
- dob: 出生日期(DD/MM/YYYY格式)
- nationality: 国籍代码(如CHN、USA、GBR、SGP等)
- sex: 性别(1=男,2=女)
- passExpDte: 护照有效期(DD/MM/YYYY格式)
- email: 邮箱地址
- confirmEmail: 确认邮箱地址(与邮箱相同)
- region: 国家电话区号(如86、1、44、65等)
- mobile: 手机号码(不含+号和国家代码)
- arrDt: 到达日期(DD/MM/YYYY格式)
- depDt: 离开日期(DD/MM/YYYY格式)
- vesselNm: 航班/交通工具号码
- trvlMode: 交通方式(1=飞机,2=陆路,3=海运)
- embark: 最后登船港国家代码(通常与nationality相同)
- accommodationStay: 住宿类型(01=酒店,02=亲友,99=其他)
- accommodationAddress1: 住宿地址第一行
- accommodationAddress2: 住宿地址第二行(可为空)
- accommodationState: 州属代码(01=柔佛,02=吉打,03=吉兰丹,04=马六甲,05=森美兰,06=彭亨,07=槟城,08=霹雳,09=玻璃市,10=雪兰莪,11=登嘉楼,12=沙巴,13=砂拉越,14=吉隆坡,15=纳闽,16=布城)
- accommodationCity: 城市名称(通常从地址中提取)
- accommodationPostcode: 邮政编码

常用代码参考：
国籍代码: CHN(中国), USA(美国), GBR(英国), SGP(新加坡), JPN(日本), KOR(韩国), THA(泰国), AUS(澳洲), IND(印度), IDN(印尼), VNM(越南), PHL(菲律宾), MYS(马来西亚)
电话区号: 86(中国), 1(美国), 44(英国), 65(新加坡), 81(日本), 82(韩国), 66(泰国), 61(澳洲), 91(印度), 62(印尼), 84(越南), 63(菲律宾), 60(马来西亚)
州属代码: 01=柔佛(JOHOR), 02=吉打(KEDAH), 03=吉兰丹(KELANTAN), 04=马六甲(MELAKA), 05=森美兰(NEGERI SEMBILAN), 06=彭亨(PAHANG), 07=槟城(PULAU PINANG), 08=霹雳(PERAK), 09=玻璃市(PERLIS), 10=雪兰莪(SELANGOR), 11=登嘉楼(TERENGGANU), 12=沙巴(SABAH), 13=砂拉越(SARAWAK), 14=吉隆坡(WP KUALA LUMPUR), 15=纳闽(WP LABUAN), 16=布城(WP PUTRAJAYA)

请仔细分析以下旅客信息，数据可能是表格格式、列表格式或文本格式。
常见的数据格式包括：
1. 表格格式: "序号 中文姓名 英文姓名 性别 出生日期 护照到期日期 护照号码 ..."
2. 列表格式: "姓名:张三, 护照号:E1234567, 性别:男 ..."
3. 文本格式: "张三先生，护照号码E1234567，男性，出生于1990年1月1日..."

解析规则：
- 如果有中文姓名和英文姓名，优先使用英文姓名
- 性别："男"=1, "女"=2
- 日期格式：将年月日转换为DD/MM/YYYY格式
- 如果信息不完整，对应字段返回null
- 根据姓名特征推断国籍（如中文姓名推断为CHN）
- 🚫 不要自动推断或填充其他字段（如电话区号、登船港等）
- 只提取用户明确提供的信息，不要根据国籍自动设置其他字段

旅客信息：
${travelerInfo}

重要提示：
1. 请仔细解析上述实际旅客信息，不要使用示例数据
2. 只返回JSON格式的数据，不要包含任何其他文字或解释
3. 根据实际旅客信息填写字段值，如果某个字段无法从提供的信息中提取，请返回null
4. 确保所有提取的数据都来自于实际的旅客信息，而不是示例或模板数据

JSON格式示例：
{
    "name": null,
    "passNo": null,
    "dob": null,
    "nationality": null,
    "sex": null,
    "passExpDte": null,
    "email": null,
    "confirmEmail": null,
    "region": null,
    "mobile": null,
    "arrDt": null,
    "depDt": null,
    "vesselNm": null,
    "trvlMode": null,
    "embark": null,
    "accommodationStay": null,
    "accommodationAddress1": null,
    "accommodationAddress2": null,
    "accommodationState": null,
    "accommodationCity": null,
    "accommodationPostcode": null
}
`;

            const response = await fetch(`${GEMINI_API_URL}?key=${apiKey}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    contents: [{
                        parts: [{
                            text: prompt
                        }]
                    }],
                    generationConfig: {
                        temperature: 0.1,
                        topK: 1,
                        topP: 1,
                        maxOutputTokens: 2048,
                    }
                })
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`Gemini API 请求失败: ${response.status} - ${errorText}`);
            }

            const data = await response.json();
            
            if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
                throw new Error('Gemini API 返回数据格式错误');
            }
            
            const content = data.candidates[0].content.parts[0].text;
            
            // 清理响应内容，提取JSON
            let jsonString = content.trim();
            if (jsonString.startsWith('```json')) {
                jsonString = jsonString.replace(/^```json\s*/, '').replace(/\s*```$/, '');
            } else if (jsonString.startsWith('```')) {
                jsonString = jsonString.replace(/^```\s*/, '').replace(/\s*```$/, '');
            }

            try {
                const parsedData = JSON.parse(jsonString);
                console.log('🔍 AI解析的原始数据:', parsedData);
                
                // 数据验证和清理
                const cleanedData = validateAndCleanData(parsedData);
                console.log('✅ 清理后的数据:', cleanedData);
                return cleanedData;
            } catch (e) {
                console.error('❌ JSON解析失败或数据验证失败:', jsonString);
                console.error('❌ 详细错误信息:', e.message);
                throw new Error(`AI返回的数据格式错误: ${e.message}`);
            }
        }

        // 数据验证和清理
        function validateAndCleanData(data) {
            const cleaned = { ...data };
            
            // 🎯 更改验证逻辑：允许字段为null，支持选择性更新
            console.log('🧹 开始数据清理和验证...');
            
            // 不再要求所有字段都必须存在，允许部分字段为null
            // const requiredFields = ['name', 'passNo', 'dob', 'nationality', 'sex', 'email', 'mobile'];
            // for (const field of requiredFields) {
            //     if (!cleaned[field]) {
            //         throw new Error(`缺少必要字段: ${field}`);
            //     }
            // }
            
            // 自动填充confirmEmail（仅当email有值时）
            if (cleaned.email && cleaned.email !== null && !cleaned.confirmEmail) {
                cleaned.confirmEmail = cleaned.email;
                console.log('🔧 自动填充confirmEmail:', cleaned.email);
            }
            
            // 🚫 禁用自动填充逻辑 - 避免意外更改未提供的字段
            // 
            // 问题：当用户只提供基本信息（姓名、护照、国籍）时，系统不应该自动更改
            // 其他下拉菜单字段（如手机号区域代码、最后登船港等）
            // 
            // 解决方案：只有当用户明确提供了某个字段的值时，才更新该字段
            
            console.log('🔍 保持字段独立性 - 跳过自动填充embark和region');
            console.log('💡 只有用户明确提供的数据才会被更新');
            
            // 原有的自动填充逻辑已禁用：
            // if (cleaned.nationality && cleaned.nationality !== null && !cleaned.embark) {
            //     cleaned.embark = cleaned.nationality;
            //     console.log('🔧 自动填充embark:', cleaned.nationality);
            // }
            // 
            // if ((!cleaned.region || cleaned.region === null) && cleaned.nationality && cleaned.nationality !== null) {
            //     const nationalityToRegion = { ... };
            //     cleaned.region = nationalityToRegion[cleaned.nationality] || '86';
            //     console.log('🔧 根据国籍自动设置区号:', cleaned.nationality, '→', cleaned.region);
            // }
            
            // 智能默认值设置（只在必要时设置）
            if ((!cleaned.trvlMode || cleaned.trvlMode === null) && cleaned.vesselNm && cleaned.vesselNm.match(/^[A-Z]{2}\d+$/)) {
                cleaned.trvlMode = '1'; // 航班格式，默认飞机
                console.log('🔧 根据航班号自动设置交通方式: 飞机');
            }
            
            if ((!cleaned.accommodationStay || cleaned.accommodationStay === null) && cleaned.accommodationAddress1) {
                cleaned.accommodationStay = '01'; // 有住宿地址，默认酒店
                console.log('🔧 根据住宿地址自动设置住宿类型: 酒店');
            }
            
            console.log('🎯 最终清理后的数据:', cleaned);
            return cleaned;
        }

        // 创建MDAC脚本
        function createMDACScript(data) {
            const timestamp = new Date().toLocaleString('zh-CN');
            
            return `// 🤖 MDAC 表单自动填充脚本 (AI Generated)
// 生成时间: ${timestamp}
// 模型: Gemini 2.5 Flash Lite
// 使用方法：在MDAC网站页面按F12打开控制台，粘贴此脚本并回车执行

(function() {
    'use strict';
    
    console.log('🚀 开始执行MDAC表单自动填充...');
    console.log('📊 数据来源: AI智能解析');
    
    // 🗃️ 表单数据
    const formData = ${JSON.stringify(data, null, 4)};
    
    // 📝 填充计数器
    let successCount = 0;
    let totalFields = 0;
    
    // 🔧 填充函数
    function fillField(selector, value, isSelect = false) {
        totalFields++;
        const element = document.querySelector(selector);
        
        if (element && value !== undefined && value !== null && value !== '') {
            try {
                if (isSelect && element.tagName === 'SELECT') {
                    // 改进的下拉菜单处理逻辑
                    return fillSelectField(element, value, selector);
                } else {
                    element.value = value;
                }
                
                // 触发事件以确保表单验证
                element.dispatchEvent(new Event('input', { bubbles: true }));
                element.dispatchEvent(new Event('change', { bubbles: true }));
                element.dispatchEvent(new Event('blur', { bubbles: true }));
                
                successCount++;
                console.log(\`✅ 填充 \${selector}: \${value}\`);
                return true;
            } catch (error) {
                console.error(\`❌ 填充失败 \${selector}:\`, error);
                return false;
            }
        } else {
            console.warn(\`⚠️ 字段不存在或值为空: \${selector}\`);
            return false;
        }
    }

    // 专门处理下拉菜单的函数
    function fillSelectField(selectElement, value, selector) {
        try {
            // 1. 移除所有现有的selected属性
            Array.from(selectElement.options).forEach(option => {
                option.removeAttribute('selected');
            });
            
            // 2. 设置新的值
            selectElement.value = value;
            
            // 3. 找到对应的选项并设置selected属性
            const targetOption = selectElement.querySelector(\`option[value="\${value}"]\`);
            if (targetOption) {
                targetOption.setAttribute('selected', '');
                selectElement.selectedIndex = Array.from(selectElement.options).indexOf(targetOption);
            } else {
                console.warn(\`⚠️ 未找到值为 "\${value}" 的选项在 \${selector}\`);
                return false;
            }
            
            // 4. 触发所有必要的事件
            const events = ['focus', 'input', 'change', 'blur'];
            events.forEach(eventType => {
                const event = new Event(eventType, { bubbles: true, cancelable: true });
                selectElement.dispatchEvent(event);
            });
            
            // 5. 验证设置是否成功
            if (selectElement.value === value) {
                successCount++;
                console.log(\`✅ 下拉菜单填充成功 \${selector}: \${value} (\${targetOption ? targetOption.text : '未知'})\`);
                return true;
            } else {
                console.error(\`❌ 下拉菜单填充失败 \${selector}: 期望值 \${value}, 实际值 \${selectElement.value}\`);
                return false;
            }
        } catch (error) {
            console.error(\`❌ 下拉菜单填充异常 \${selector}:\`, error);
            return false;
        }
    }

    // ⏰ 等待函数
    function wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 🔄 重试机制的下拉菜单填充函数
    async function fillFieldWithRetry(selector, value, isSelect = false, maxRetries = 3, delay = 500) {
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            console.log(\`🔄 尝试填充 \${selector}, 第 \${attempt}/\${maxRetries} 次\`);
            
            const success = fillField(selector, value, isSelect);
            
            if (success) {
                console.log(\`✅ 成功填充 \${selector} (第 \${attempt} 次尝试)\`);
                return true;
            }
            
            if (attempt < maxRetries) {
                console.log(\`⏳ 等待 \${delay}ms 后重试...\`);
                await wait(delay);
                // 递增延迟时间
                delay *= 1.5;
            } else {
                console.error(\`❌ 填充失败 \${selector}, 已达到最大重试次数 \${maxRetries}\`);
                return false;
            }
        }
        return false;
    }

    // 🎯 选择性字段更新函数 - 只更新Gemini返回的非空字段
    function fillFieldSelectively(selector, value, isSelect = false, fieldName = '') {
        // 跳过空值、null值和undefined值
        if (value === null || value === undefined || value === '') {
            console.log(\`⏸️ 跳过空字段 \${fieldName} (\${selector})\`);
            return false;
        }
        
        return fillField(selector, value, isSelect);
    }

    // 🏙️ 改进的城市选择函数，支持州属变化后的城市加载
    async function selectCityWithStateLoad(stateCode, addressLine1, maxWaitTime = 5000) {
        console.log(\`🏛️ 开始处理州属 \${stateCode} 的城市选择\`);
        
        // 等待城市下拉菜单加载
        const startTime = Date.now();
        while (Date.now() - startTime < maxWaitTime) {
            const citySelect = document.querySelector('#accommodationCity');
            if (citySelect && citySelect.options.length > 1) {
                console.log(\`🏙️ 城市列表已加载，共 \${citySelect.options.length - 1} 个选项\`);
                return selectCity(stateCode, addressLine1);
            }
            
            console.log('⏳ 等待城市列表加载...');
            await wait(500);
        }
        
        console.warn(\`⚠️ 城市列表加载超时 (\${maxWaitTime}ms)\`);
        return false;
    }
    
    // 🎯 智能城市选择 - 基于MD文档完整城市列表
    function selectCity(stateCode, addressLine1) {
        const citySelect = document.querySelector('#accommodationCity');
        if (!citySelect || citySelect.options.length <= 1) {
            console.log('🏙️ 城市列表未加载，等待重试...');
            return false;
        }
        
        // 完整的州属城市映射 - 基于MD文档数据
        const cityMappings = {
            '01': { // 柔佛州 (67个城市)
                keywords: {
                    'legoland': 'JOHOR BAHRU', 'johor bahru': 'JOHOR BAHRU', 'jb': 'JOHOR BAHRU',
                    'nusajaya': 'JOHOR BAHRU', 'iskandar': 'ISKANDAR PUTERI', 'bukit indah': 'JOHOR BAHRU',
                    'skudai': 'SKUDAI', 'masai': 'MASAI', 'pasir gudang': 'PASIR GUDANG',
                    'kluang': 'KLUANG', 'batu pahat': 'BATU PAHAT', 'muar': 'MUAR',
                    'pontian': 'PONTIAN', 'segamat': 'SEGAMAT', 'mersing': 'MERSING',
                    'kota tinggi': 'KOTA TINGGI', 'kulai': 'KULAI', 'senai': 'SENAI',
                    'ulu tiram': 'ULU TIRAM', 'gelang patah': 'GELANG PATAH',
                    'taman': 'JOHOR BAHRU', 'desa': 'JOHOR BAHRU', 'bandar': 'JOHOR BAHRU'
                }
            },
            '02': { // 吉打州 (37个城市)
                keywords: {
                    'alor setar': 'ALOR SETAR', 'alor star': 'ALOR SETAR', 'kangar': 'SUNGAI PETANI',
                    'sungai petani': 'SUNGAI PETANI', 'kulim': 'KULIM', 'baling': 'BALING',
                    'langkawi': 'LANGKAWI', 'jitra': 'JITRA', 'kuala kedah': 'KUALA KEDAH',
                    'padang serai': 'PADANG SERAI', 'pendang': 'PENDANG'
                }
            },
            '03': { // 吉兰丹州 (22个城市)
                keywords: {
                    'kota bharu': 'KOTA BHARU', 'kota bahru': 'KOTA BHARU', 'kb': 'KOTA BHARU',
                    'bachok': 'BACHOK', 'pasir mas': 'PASIR MAS', 'tumpat': 'TUMPAT',
                    'kuala krai': 'KUALA KRAI', 'gua musang': 'GUA MUSANG', 'machang': 'MACHANG',
                    'tanah merah': 'TANAH MERAH', 'rantau panjang': 'RANTAU PANJANG'
                }
            },
            '04': { // 马六甲州 (21个城市)
                keywords: {
                    'melaka': 'MELAKA', 'malacca': 'MELAKA', 'alor gajah': 'ALOR GAJAH',
                    'jasin': 'JASIN', 'masjid tanah': 'MASJID TANAH', 'merlimau': 'MERLIMAU',
                    'ayer keroh': 'AYER KEROH', 'batu berendam': 'BATU BERENDAM'
                }
            },
            '05': { // 森美兰州 (51个城市)
                keywords: {
                    'seremban': 'SEREMBAN', 'port dickson': 'PORT DICKSON', 'pd': 'PORT DICKSON',
                    'nilai': 'NILAI', 'bahau': 'BAHAU', 'kuala pilah': 'KUALA PILAH',
                    'rembau': 'REMBAU', 'tampin': 'TAMPIN', 'sri menanti': 'SRI MENANTI',
                    'labu': 'LABU', 'lukut': 'LUKUT'
                }
            },
            '06': { // 彭亨州 (39个城市)
                keywords: {
                    'kuantan': 'KUANTAN', 'temerloh': 'TEMERLOH', 'bentong': 'BENTONG',
                    'raub': 'RAUB', 'jerantut': 'JERANTUT', 'pekan': 'PEKAN',
                    'cameron highlands': 'C HIGHLANDS', 'genting': 'GENTING', 'fraser': 'BUKIT FRASER',
                    'pahang': 'KUANTAN', 'mentakab': 'MENTAKAB', 'maran': 'MARAN'
                }
            },
            '07': { // 槟城州 (25个城市)
                keywords: {
                    'georgetown': 'GEORGETOWN', 'penang': 'GEORGETOWN', 'bukit mertajam': 'BUKIT MERTAJAM',
                    'butterworth': 'BUTTERWORTH', 'bayan lepas': 'BAYAN LEPAS', 'bayan baru': 'BAYAN BARU',
                    'ayer itam': 'AYER ITAM', 'balik pulau': 'BALIK PULAU', 'batu ferringhi': 'BATU FERRINGHI',
                    'gelugor': 'GELUGOR', 'nibong tebal': 'NIBONG TEBAL', 'permatang pauh': 'PERMATANG PAUH',
                    'perai': 'PERAI', 'tanjong bunga': 'TANJONG BUNGA', 'jelutong': 'JELUTONG'
                }
            },
            '08': { // 霹雳州 (88个城市)
                keywords: {
                    'ipoh': 'IPOH', 'taiping': 'TAIPING', 'teluk intan': 'TELUK INTAN',
                    'kampar': 'KAMPAR', 'batu gajah': 'BATU GAJAH', 'kuala kangsar': 'KUALA KANGSAR',
                    'lumut': 'LUMUT', 'sitiawan': 'SITIAWAN', 'pangkor': 'PANGKOR',
                    'tanjung malim': 'TANJUNG MALIM', 'sungai siput': 'SUNGAI SIPUT',
                    'slim river': 'SLIM RIVER', 'tapah': 'TAPAH', 'parit buntar': 'PARIT BUNTAR',
                    'selama': 'SELAMA', 'gopeng': 'GOPENG', 'menglembu': 'MENGLEMBU'
                }
            },
            '09': { // 玻璃市州 (6个城市)
                keywords: {
                    'kangar': 'KANGAR', 'arau': 'ARAU', 'kuala perlis': 'KUALA PERLIS',
                    'padang besar': 'PADANG BESAR', 'perlis': 'KANGAR'
                }
            },
            '10': { // 雪兰莪州 (65个城市)
                keywords: {
                    'shah alam': 'SHAH ALAM', 'petaling jaya': 'PETALING JAYA', 'pj': 'PETALING JAYA',
                    'subang jaya': 'SUBANG JAYA', 'subang': 'SUBANG JAYA', 'klang': 'KLANG',
                    'kajang': 'KAJANG', 'ampang': 'AMPANG', 'puchong': 'PUCHONG',
                    'cyberjaya': 'CYBERJAYA', 'sepang': 'SEPANG', 'rawang': 'RAWANG',
                    'banting': 'BANTING', 'kuala selangor': 'KUALA SELANGOR', 'seri kembangan': 'SERI KEMBANGAN',
                    'semenyih': 'SEMENYIH', 'sungai buloh': 'SUNGAI BULOH', 'selayang': 'SELAYANG',
                    'gombak': 'GOMBAK', 'serdang': 'SERDANG', 'cheras': 'CHERAS',
                    'bangi': 'BDR. BARU BANGI', 'dengkil': 'DENGKIL'
                }
            },
            '11': { // 登嘉楼州 (21个城市)
                keywords: {
                    'kuala terengganu': 'KUALA TERENGGANU', 'kt': 'KUALA TERENGGANU',
                    'kemaman': 'KEMAMAN', 'dungun': 'DUNGUN', 'marang': 'MARANG',
                    'besut': 'BESUT', 'kuala besut': 'KUALA BESUT', 'jertih': 'JERTIH',
                    'kerteh': 'KERTEH', 'paka': 'PAKA', 'kuala nerus': 'KUALA NERUS'
                }
            },
            '12': { // 沙巴州 (32个城市)
                keywords: {
                    'kota kinabalu': 'KOTA KINABALU', 'kk': 'KOTA KINABALU', 'sandakan': 'SANDAKAN',
                    'tawau': 'TAWAU', 'lahad datu': 'LAHAD DATU', 'keningau': 'KENINGAU',
                    'ranau': 'RANAU', 'kudat': 'KUDAT', 'semporna': 'SEMPORNA',
                    'beaufort': 'BEAUFORT', 'papar': 'PAPAR', 'penampang': 'PENAMPANG',
                    'tuaran': 'TUARAN', 'tenom': 'TENOM', 'tambunan': 'TAMBUNAN'
                }
            },
            '13': { // 砂拉越州 (68个城市)
                keywords: {
                    'kuching': 'KUCHING', 'miri': 'MIRI', 'sibu': 'SIBU', 'bintulu': 'BINTULU',
                    'sri aman': 'SRI AMAN', 'sarikei': 'SARIKEI', 'kapit': 'KAPIT',
                    'limbang': 'LIMBANG', 'lawas': 'LAWAS', 'mukah': 'MUKAH',
                    'betong': 'BETONG', 'serian': 'SERIAN', 'bau': 'BAU',
                    'lundu': 'LUNDU', 'simunjan': 'SIMUNJAN'
                }
            },
            '14': { // 吉隆坡 (3个城市)
                keywords: {
                    'kuala lumpur': 'KUALA LUMPUR', 'kl': 'KUALA LUMPUR',
                    'bukit bintang': 'KUALA LUMPUR', 'cheras': 'CHERAS',
                    'batu caves': 'BATU CAVES', 'klcc': 'KUALA LUMPUR',
                    'petaling': 'KUALA LUMPUR', 'ampang': 'KUALA LUMPUR'
                }
            },
            '15': { // 纳闽 (1个城市)
                keywords: {
                    'labuan': 'W.P LABUAN', 'wp labuan': 'W.P LABUAN'
                }
            },
            '16': { // 布城 (1个城市)
                keywords: {
                    'putrajaya': 'PUTRAJAYA', 'putra': 'PUTRAJAYA'
                }
            }
        };
        
        if (!addressLine1 || !stateCode) {
            console.log('🏙️ 地址或州属信息不完整');
            return false;
        }
        
        const stateMappings = cityMappings[stateCode];
        if (!stateMappings) {
            console.log(\`🏙️ 州属代码 \${stateCode} 未配置城市映射\`);
            return false;
        }
        
        const addressLower = addressLine1.toLowerCase();
        let targetCity = null;
        let matchedKeyword = '';
        
        // 查找匹配的城市 - 优先匹配较长的关键词
        const sortedKeywords = Object.keys(stateMappings.keywords).sort((a, b) => b.length - a.length);
        
        for (const keyword of sortedKeywords) {
            if (addressLower.includes(keyword)) {
                targetCity = stateMappings.keywords[keyword];
                matchedKeyword = keyword;
                break;
            }
        }
        
        if (!targetCity) {
            console.log(\`🏙️ 在地址 "\${addressLine1}" 中未找到已知的城市关键词\`);
            // 尝试模糊匹配
            for (const option of citySelect.options) {
                const cityName = option.text.toLowerCase();
                if (addressLower.includes(cityName) || cityName.includes(addressLower)) {
                    option.selected = true;
                    citySelect.value = option.value;
                    citySelect.dispatchEvent(new Event('change', { bubbles: true }));
                    console.log(\`🏙️ 通过模糊匹配选择城市: \${option.text}\`);
                    return true;
                }
            }
            return false;
        }
        
        // 在下拉菜单中查找并选择城市
        for (const option of citySelect.options) {
            const optionText = option.text.toUpperCase().trim();
            const targetCityUpper = targetCity.toUpperCase().trim();
            
            // 尝试多种匹配方式
            if (optionText.includes(targetCityUpper) || 
                targetCityUpper.includes(optionText) ||
                optionText === targetCityUpper ||
                optionText.replace(/\s+/g, '').includes(targetCityUpper.replace(/\s+/g, ''))) {
                
                option.selected = true;
                citySelect.value = option.value;
                
                // 触发change事件，确保网站识别选择
                const changeEvent = new Event('change', { bubbles: true });
                citySelect.dispatchEvent(changeEvent);
                
                // 额外触发其他可能需要的事件
                setTimeout(() => {
                    citySelect.dispatchEvent(new Event('blur', { bubbles: true }));
                    citySelect.dispatchEvent(new Event('input', { bubbles: true }));
                }, 100);
                
                console.log(\`🏙️ 成功选择城市: \${option.text} (值: \${option.value}, 匹配关键词: \${matchedKeyword})\`);
                return true;
            }
        }
        
        console.log(\`🏙️ 在下拉菜单中未找到城市: \${targetCity}\`);
        console.log('🏙️ 可用城市选项:', Array.from(citySelect.options).slice(0, 10).map(o => \`\${o.text} (\${o.value})\`).join(', ') + '...');
        return false;
    }

    // 🚀 异步填充表单
    async function fillForm() {
        try {
            console.log('📋 开始选择性填充个人信息...');
            console.log('💡 只更新Gemini返回的非空字段，保留现有表单内容');
            
            // 个人信息部分 - 选择性更新
            fillFieldSelectively('#name', formData.name, false, '姓名');
            fillFieldSelectively('#passNo', formData.passNo, false, '护照号');
            fillFieldSelectively('#dob', formData.dob, false, '出生日期');
            fillFieldSelectively('#nationality', formData.nationality, true, '国籍');
            
            // 等待nationality的onchange事件处理完成，然后设置region
            await wait(1500);
            
            fillFieldSelectively('#sex', formData.sex, true, '性别');
            fillFieldSelectively('#passExpDte', formData.passExpDte, false, '护照到期日');
            fillFieldSelectively('#email', formData.email, false, '邮箱');
            fillFieldSelectively('#confirmEmail', formData.confirmEmail, false, '确认邮箱');
            
            // 地区代码 - 延时填充，确保覆盖nationality自动设置的值
            if (formData.region && formData.region !== null && formData.region !== '') {
                await fillFieldWithRetry('#region', formData.region, true);
                console.log('🔧 设置电话区号字段 (#region):', formData.region);
                
                // 再次确保region值被正确设置
                await wait(500);
                const regionField = document.querySelector('#region');
                if (regionField && regionField.value !== formData.region) {
                    regionField.value = formData.region;
                    regionField.dispatchEvent(new Event('change', { bubbles: true }));
                    console.log('🔧 二次确认设置region:', formData.region);
                }
            } else {
                console.log('⏸️ 跳过空字段 地区代码 (#region)');
            }
            
            fillFieldSelectively('#mobile', formData.mobile, false, '手机号');

            await wait(800);

            console.log('✈️ 开始选择性填充旅行信息...');
            
            // 旅行信息部分 - 选择性更新
            fillFieldSelectively('#arrDt', formData.arrDt, false, '到达日期');
            fillFieldSelectively('#depDt', formData.depDt, false, '离开日期');
            fillFieldSelectively('#vesselNm', formData.vesselNm, false, '航班号');
            fillFieldSelectively('#trvlMode', formData.trvlMode, true, '交通方式');
            fillFieldSelectively('#embark', formData.embark, true, '出发地');

            await wait(800);

            console.log('🏠 开始选择性填充住宿信息...');
            
            // 住宿信息部分 - 选择性更新
            fillFieldSelectively('#accommodationStay', formData.accommodationStay, true, '住宿类型');
            fillFieldSelectively('#accommodationAddress1', formData.accommodationAddress1, false, '住宿地址1');
            fillFieldSelectively('#accommodationAddress2', formData.accommodationAddress2, false, '住宿地址2');
            fillFieldSelectively('#accommodationPostcode', formData.accommodationPostcode, false, '邮政编码');
            
            // 州属选择 - 特殊处理，因为它会触发城市列表加载
            let stateChanged = false;
            if (formData.accommodationState && formData.accommodationState !== null && formData.accommodationState !== '') {
                const stateElement = document.querySelector('#accommodationState');
                const currentState = stateElement ? stateElement.value : '';
                
                if (currentState !== formData.accommodationState) {
                    console.log(\`🏛️ 州属将从 "\${currentState}" 更改为 "\${formData.accommodationState}"\`);
                    fillFieldSelectively('#accommodationState', formData.accommodationState, true, '州属');
                    stateChanged = true;
                } else {
                    console.log(\`🏛️ 州属保持不变: "\${currentState}"\`);
                }
            } else {
                console.log('⏸️ 跳过空字段 州属 (#accommodationState)');
            }

            // 城市选择 - 仅在州属改变或有地址信息时处理
            let citySelected = false;
            if (stateChanged || (formData.accommodationAddress1 && formData.accommodationAddress1 !== null && formData.accommodationAddress1 !== '')) {
                console.log('🏙️ 开始处理城市选择...');
                citySelected = await selectCityWithStateLoad(formData.accommodationState, formData.accommodationAddress1);
                
                if (citySelected) {
                    console.log('✅ 城市选择成功');
                } else {
                    console.log('❌ 城市选择失败或超时，需要手动选择');
                }
            } else {
                console.log('⏸️ 跳过城市选择（州属未变更且无地址信息）');
            }
            
            if (!citySelected) {
                console.log('⚠️ 自动城市选择失败，请手动选择城市');
                
                // 尝试显示可用的城市选项供用户参考
                const citySelect = document.querySelector('#accommodationCity');
                if (citySelect && citySelect.options.length > 1) {
                    console.log('🏙️ 可用城市选项:');
                    Array.from(citySelect.options).slice(1, 11).forEach((option, index) => {
                        console.log(\`  \${index + 1}. \${option.text}\`);
                    });
                    if (citySelect.options.length > 11) {
                        console.log(\`  ... 还有 \${citySelect.options.length - 11} 个城市\`);
                    }
                }
            }

            // 检查城市字段状态
            const citySelect = document.querySelector('#accommodationCity');
            if (citySelect) {
                const selectedCity = citySelect.options[citySelect.selectedIndex];
                if (selectedCity && selectedCity.value && selectedCity.value !== '') {
                    console.log(\`🏙️ 当前选择的城市: \${selectedCity.text} (值: \${selectedCity.value})\`);
                } else {
                    console.log('🏙️ 城市字段未选择，建议手动选择');
                }
            }

            await wait(500);

            // 填充邮政编码
            fillField('#accommodationPostcode', formData.accommodationPostcode);

            // 检查城市字段是否被选择
            const cityElement = document.querySelector('#accommodationCity');
            if (cityElement && cityElement.value && cityElement.value !== '') {
                successCount++;
                console.log(\`✅ city: \${cityElement.options[cityElement.selectedIndex].text}\`);
            } else {
                console.log('❌ city: 未选择（需要手动选择）');
            }
            totalFields++; // 将城市字段加入总数

            // 📊 填充结果统计
            console.log(\`\\n📊 填充完成统计:\`);
            console.log(\`✅ 成功填充: \${successCount}/\${totalFields} 个字段\`);
            console.log(\`📝 成功率: \${((successCount/totalFields)*100).toFixed(1)}%\`);
            console.log('🎉 MDAC表单自动填充完成！请检查并提交表单。');

        } catch (error) {
            console.error('❌ 填充过程中出现错误:', error);
            alert(\`❌ 填充过程中出现错误: \${error.message}\\n\\n请检查控制台日志获取详细信息。\`);
        }
    }

    // 🚀 执行填充
    fillForm();
})();

// 🔗 更多工具和支持
console.log('🔗 MDAC工具支持: https://github.com/your-repo/mdac-tools');
console.log('💝 如果这个脚本对您有帮助，欢迎给项目点星！');`;
        }

        // 从地址获取城市关键词
        function getCityKeywordFromAddress(address, stateCode) {
            if (!address || !stateCode) return '';
            
            const addressLower = address.toLowerCase();
            
            // 使用与selectCity相同的映射逻辑
            const cityMappings = {
                '01': { 'legoland': 'johor bahru', 'johor bahru': 'johor bahru', 'jb': 'johor bahru', 'nusajaya': 'johor bahru' },
                '07': { 'georgetown': 'georgetown', 'penang': 'georgetown', 'butterworth': 'butterworth' },
                '10': { 'shah alam': 'shah alam', 'petaling jaya': 'petaling jaya', 'pj': 'petaling jaya', 'subang': 'subang jaya' },
                '14': { 'kuala lumpur': 'kuala lumpur', 'kl': 'kuala lumpur', 'cheras': 'cheras' }
            };
            
            const stateMapping = cityMappings[stateCode];
            if (stateMapping) {
                for (const [keyword, city] of Object.entries(stateMapping)) {
                    if (addressLower.includes(keyword)) {
                        return city;
                    }
                }
            }
            
            return address.split(',')[0].trim(); // 返回地址第一部分作为fallback
        }

        // 生成书签脚本
        function generateBookmarkScript() {
            try {
                // 保存持久化数据
                savePersistentData();
                
                // 收集表单数据
                const formData = collectFormData();
                
                // 验证数据
                validateFormData(formData);

                // 生成压缩的书签脚本
                const cityKeyword = getCityKeywordFromAddress(formData.accommodationAddress1, formData.accommodationState);
                const bookmarkScript = `javascript:(function(){
const data=${JSON.stringify(formData)};
Object.keys(data).forEach(k=>{const e=document.getElementById(k);if(e){e.value=data[k];e.dispatchEvent(new Event('change'))}});
setTimeout(()=>{
const c=document.getElementById('city');
if(c && c.options.length>1){
const keywords=['${cityKeyword}','${formData.accommodationAddress1?.toLowerCase()}'];
for(let keyword of keywords){
if(!keyword) continue;
for(let o of c.options){
if(o.text.toLowerCase().includes(keyword.toLowerCase())){
o.selected=true;c.value=o.value;
c.dispatchEvent(new Event('change'));
console.log('🏙️ 选择城市:',o.text);
return;
}}}
console.log('⚠️ 未找到匹配城市，请手动选择');
}},2000);
console.log('✅ MDAC表单已填充完成');
})();`;

                document.getElementById('outputCode').textContent = `// 📌 MDAC 书签脚本
// 使用方法：
// 1. 复制下面的完整代码
// 2. 在浏览器书签栏右键 → 添加书签
// 3. 名称填写：MDAC快速填充
// 4. 网址粘贴下面的代码
// 5. 在MDAC页面点击书签即可一键填充

${bookmarkScript}

// 💡 提示：书签脚本已针对您的数据定制
// 🔄 如需更新数据，请重新生成书签脚本`;

                showStatus('🔖 书签脚本已生成！', 'success');
                
            } catch (error) {
                showStatus(`❌ ${error.message}`, 'error');
            }
        }

        // 存储原始脚本供重置使用
        let originalScript = '';

        // 复制脚本到剪贴板
        function copyScript() {
            const outputCode = document.getElementById('outputCode');
            const textToCopy = outputCode.value || outputCode.textContent;
            
            if (!textToCopy || textToCopy.includes('等待您输入旅客信息')) {
                showStatus('请先生成脚本', 'error');
                return;
            }

            navigator.clipboard.writeText(textToCopy).then(() => {
                showStatus('✅ 脚本已复制到剪贴板！', 'success');
                
                // 临时改变按钮文字
                const copyBtn = document.querySelector('.copy-btn');
                const originalText = copyBtn.textContent;
                copyBtn.textContent = '✅ 已复制!';
                copyBtn.style.background = '#28a745';
                
                setTimeout(() => {
                    copyBtn.textContent = originalText;
                    copyBtn.style.background = '#3498db';
                }, 3000);
            }).catch(err => {
                console.error('复制失败:', err);
                showStatus('复制失败，请手动选择复制', 'error');
                
                // 选中文本作为备选方案
                outputCode.select();
            });
        }

        // 格式化脚本 - 美化代码便于编辑
        function beautifyScript() {
            const outputCode = document.getElementById('outputCode');
            if (!outputCode.value || outputCode.value.includes('等待您输入')) {
                showStatus('❌ 请先生成脚本', 'error');
                return;
            }

            try {
                // 简单的JavaScript格式化
                let script = outputCode.value;
                
                // 添加适当的缩进和换行
                script = script
                    .replace(/{\s*/g, ' {\n    ')
                    .replace(/;\s*/g, ';\n    ')
                    .replace(/}\s*/g, '\n}\n')
                    .replace(/,\s*/g, ',\n        ')
                    .replace(/\s+/g, ' ')
                    .replace(/\n\s*\n/g, '\n')
                    .replace(/    }/g, '}');

                outputCode.value = script;
                showStatus('✅ 脚本已格式化', 'success');
            } catch (error) {
                showStatus('❌ 格式化失败', 'error');
            }
        }

        // 压缩脚本 - 移除不必要的空白和注释
        function minifyScript() {
            const outputCode = document.getElementById('outputCode');
            if (!outputCode.value || outputCode.value.includes('等待您输入')) {
                showStatus('❌ 请先生成脚本', 'error');
                return;
            }

            try {
                let script = outputCode.value;
                
                // 简单的压缩：移除注释和多余空白
                script = script
                    .replace(/\/\*[\s\S]*?\*\//g, '') // 移除块注释
                    .replace(/\/\/.*$/gm, '') // 移除行注释
                    .replace(/\s+/g, ' ') // 压缩空白
                    .replace(/;\s*}/g, ';}') // 优化分号和大括号
                    .replace(/\s*{\s*/g, '{')
                    .replace(/\s*}\s*/g, '}')
                    .replace(/\s*,\s*/g, ',')
                    .replace(/\s*;\s*/g, ';')
                    .trim();

                outputCode.value = script;
                showStatus('✅ 脚本已压缩', 'success');
            } catch (error) {
                showStatus('❌ 压缩失败', 'error');
            }
        }

        // 重置到原始脚本
        function resetScript() {
            const outputCode = document.getElementById('outputCode');
            if (!originalScript) {
                showStatus('❌ 没有原始脚本可以重置', 'error');
                return;
            }

            outputCode.value = originalScript;
            showStatus('✅ 已重置为原始脚本', 'success');
        }

        // 显示状态信息
        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';
            
            // 自动隐藏成功和信息消息
            if (type === 'success' || type === 'info') {
                setTimeout(() => {
                    statusDiv.style.display = 'none';
                }, 5000);
            }
        }

        // 自动解析功能
        let autoParseTimeout = null;
        let isAutoParseEnabled = true;
        let lastProcessedText = '';

        function setupAutoAnalysis() {
            const textarea = document.getElementById('travelerInfo');
            const indicator = document.getElementById('autoParseIndicator');
            
            textarea.addEventListener('input', function() {
                // 自动调整文本框高度
                this.style.height = 'auto';
                this.style.height = (this.scrollHeight) + 'px';
                
                // 自动AI解析逻辑
                const currentText = this.value.trim();
                
                // 清除之前的定时器和视觉效果
                if (autoParseTimeout) {
                    clearTimeout(autoParseTimeout);
                }
                textarea.classList.remove('auto-parse-active');
                indicator.style.display = 'none';
                
                // 检查是否满足自动解析条件
                if (isAutoParseEnabled && 
                    currentText.length > 10 && 
                    currentText !== lastProcessedText) {
                    
                    // 显示视觉指示器
                    textarea.classList.add('auto-parse-active');
                    indicator.style.display = 'block';
                    showStatus('⚡ 1秒后自动解析...', 'info');
                    
                    // 设置1秒延迟自动解析
                    autoParseTimeout = setTimeout(async () => {
                        console.log('🚀 自动触发AI解析...');
                        textarea.classList.remove('auto-parse-active');
                        indicator.style.display = 'none';
                        lastProcessedText = currentText;
                        await performAIAnalysis();
                    }, 1000);
                } else if (currentText.length <= 10) {
                    // 清除状态提示
                    document.getElementById('status').style.display = 'none';
                }
            });
            
            // 手动点击按钮时禁用自动解析，避免重复处理
            document.getElementById('aiParseBtn').addEventListener('click', function() {
                if (autoParseTimeout) {
                    clearTimeout(autoParseTimeout);
                    autoParseTimeout = null;
                }
                textarea.classList.remove('auto-parse-active');
                indicator.style.display = 'none';
                lastProcessedText = document.getElementById('travelerInfo').value.trim();
            });
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 MDAC脚本生成器已就绪');
            
            // 加载持久化数据
            loadPersistentData();
            
            // 设置自动解析功能
            setupAutoAnalysis();
            
            // 监听持久化字段变化，自动保存
            document.getElementById('persistentEmail').addEventListener('blur', savePersistentData);
            document.getElementById('persistentPhone').addEventListener('blur', savePersistentData);
            document.getElementById('phoneRegion').addEventListener('change', function() {
                savePersistentData();
                console.log('📞 电话区号已更改:', this.value);
            });
            
            // 移除国籍与电话区号的自动同步机制
            // 保持字段独立性，用户可以独立设置国籍和电话区号
            console.log('🔒 字段独立性已启用 - 国籍和电话区号字段保持独立');
        });

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            // Ctrl+Enter 快速生成
            if (e.ctrlKey && e.key === 'Enter') {
                e.preventDefault();
                document.getElementById('generateBtn').click();
            }
            
            // Ctrl+K 清空表单
            if (e.ctrlKey && e.key === 'k') {
                e.preventDefault();
                clearForm();
            }
        });
    </script>
</body>
</html> 