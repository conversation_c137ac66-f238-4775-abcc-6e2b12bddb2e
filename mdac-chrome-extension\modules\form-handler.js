// MDAC智能填充助手 - 表单处理模块
// 负责表单数据收集、填充和持久化存储

import { STORAGE_KEYS, FIELD_MAPPING, SAMPLE_DATA } from './config.js';

/**
 * 从popup表单收集数据
 * @returns {Object} 表单数据对象
 */
export function collectFormData() {
    console.log('📋 开始收集表单数据...');
    
    // 获取持久化数据
    const persistentEmail = document.getElementById('email')?.value.trim() || '';
    const persistentPhone = document.getElementById('mobile')?.value.trim() || '';
    const phoneRegion = document.getElementById('phoneRegion')?.value || '60';
    
    const formData = {
        name: document.getElementById('passengerName')?.value.trim() || '',
        passNo: document.getElementById('passportNo')?.value.trim() || '',
        dob: document.getElementById('birthDate')?.value.trim() || '',
        nationality: document.getElementById('nationality')?.value || '',
        sex: document.getElementById('gender')?.value || '',
        passExpDte: document.getElementById('passportExpiry')?.value.trim() || '',
        email: persistentEmail,
        confirmEmail: persistentEmail,
        region: phoneRegion,
        mobile: persistentPhone,
        arrDt: document.getElementById('arrivalDate')?.value.trim() || '',
        depDt: document.getElementById('departureDate')?.value.trim() || '',
        vesselNm: document.getElementById('vesselName')?.value.trim() || '',
        trvlMode: document.getElementById('travelMode')?.value || '1',
        embark: document.getElementById('nationality')?.value || '', // 默认与国籍相同
        accommodationStay: '01', // 默认酒店
        accommodationAddress1: document.getElementById('accommodationAddress')?.value.trim() || '',
        accommodationAddress2: '',
        accommodationState: document.getElementById('accommodationState')?.value || '',
        accommodationCity: '', // 由脚本智能选择
        accommodationPostcode: document.getElementById('accommodationPostcode')?.value.trim() || ''
    };
    
    console.log('✅ 表单数据收集完成:', formData);
    return formData;
}

/**
 * 填充popup表单字段
 * @param {Object} data - 要填充的数据
 */
export function fillFormFields(data) {
    console.log('📝 开始填充表单字段...', data);
    
    const fieldMappings = [
        { id: 'passengerName', key: 'name' },
        { id: 'passportNo', key: 'passNo' },
        { id: 'birthDate', key: 'dob' },
        { id: 'nationality', key: 'nationality' },
        { id: 'gender', key: 'sex' },
        { id: 'passportExpiry', key: 'passExpDte' },
        { id: 'email', key: 'email' },
        { id: 'phoneRegion', key: 'region' },
        { id: 'mobile', key: 'mobile' },
        { id: 'arrivalDate', key: 'arrDt' },
        { id: 'departureDate', key: 'depDt' },
        { id: 'vesselName', key: 'vesselNm' },
        { id: 'travelMode', key: 'trvlMode' },
        { id: 'accommodationAddress', key: 'accommodationAddress1' },
        { id: 'accommodationState', key: 'accommodationState' },
        { id: 'accommodationPostcode', key: 'accommodationPostcode' }
    ];
    
    fieldMappings.forEach(({ id, key }) => {
        const element = document.getElementById(id);
        const value = data[key];
        
        if (element && value !== null && value !== undefined && value !== '') {
            element.value = value;
            console.log(`✅ 填充字段 ${id}: ${value}`);
        }
    });
    
    console.log('✅ 表单字段填充完成');
}

/**
 * 选择性填充表单字段（只填充非空值）
 * @param {Object} data - AI解析的数据
 */
export function fillFormFieldsSelectively(data) {
    console.log('🎯 开始选择性填充表单字段...', data);
    
    const fieldMappings = [
        { id: 'passengerName', key: 'name' },
        { id: 'passportNo', key: 'passNo' },
        { id: 'birthDate', key: 'dob' },
        { id: 'nationality', key: 'nationality' },
        { id: 'gender', key: 'sex' },
        { id: 'passportExpiry', key: 'passExpDte' },
        { id: 'email', key: 'email' },
        { id: 'phoneRegion', key: 'region' },
        { id: 'mobile', key: 'mobile' },
        { id: 'arrivalDate', key: 'arrDt' },
        { id: 'departureDate', key: 'depDt' },
        { id: 'vesselName', key: 'vesselNm' },
        { id: 'travelMode', key: 'trvlMode' },
        { id: 'accommodationAddress', key: 'accommodationAddress1' },
        { id: 'accommodationState', key: 'accommodationState' },
        { id: 'accommodationPostcode', key: 'accommodationPostcode' }
    ];
    
    let filledCount = 0;
    
    fieldMappings.forEach(({ id, key }) => {
        const element = document.getElementById(id);
        const value = data[key];
        
        // 只填充非空值
        if (element && value !== null && value !== undefined && value !== '') {
            element.value = value;
            filledCount++;
            console.log(`✅ 选择性填充字段 ${id}: ${value}`);
        } else if (value === null || value === undefined || value === '') {
            console.log(`⏸️ 跳过空字段 ${id}`);
        }
    });
    
    console.log(`✅ 选择性填充完成，共填充 ${filledCount} 个字段`);
    return filledCount;
}

/**
 * 清空表单
 */
export function clearForm() {
    console.log('🗑️ 清空表单...');
    
    const formElements = [
        'travelerInfo',
        'passengerName',
        'passportNo', 
        'birthDate',
        'nationality',
        'gender',
        'passportExpiry',
        'email',
        'phoneRegion',
        'mobile',
        'arrivalDate',
        'departureDate',
        'vesselName',
        'travelMode',
        'accommodationAddress',
        'accommodationState',
        'accommodationPostcode'
    ];
    
    formElements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            if (element.tagName === 'SELECT') {
                element.selectedIndex = 0;
            } else {
                element.value = '';
            }
        }
    });
    
    console.log('✅ 表单已清空');
}

/**
 * 填入示例数据
 */
export function fillSampleData() {
    console.log('📋 填入示例数据...');
    
    // 填充AI输入框
    const travelerInfo = document.getElementById('travelerInfo');
    if (travelerInfo) {
        travelerInfo.value = `李明，中国护照G12345678，1990年1月1日出生，男性，护照2026年1月1日到期。
邮箱：<EMAIL>，手机：+60123456789
计划2025年8月1日到达马来西亚，8月7日离开
乘坐MH123航班，住宿地址：吉隆坡市中心酒店，邮编50000`;
    }
    
    // 填充表单字段
    fillFormFields(SAMPLE_DATA);
    
    console.log('✅ 示例数据填入完成');
}

/**
 * 验证表单数据
 * @param {Object} data - 要验证的数据
 * @returns {Object} 验证结果 {isValid: boolean, errors: string[]}
 */
export function validateFormData(data) {
    console.log('🔍 开始验证表单数据...');
    
    const errors = [];
    const requiredFields = [
        { key: 'name', label: '姓名' },
        { key: 'passNo', label: '护照号码' },
        { key: 'dob', label: '出生日期' },
        { key: 'nationality', label: '国籍' },
        { key: 'sex', label: '性别' },
        { key: 'email', label: '邮箱地址' },
        { key: 'mobile', label: '手机号码' },
        { key: 'arrDt', label: '到达日期' },
        { key: 'depDt', label: '离开日期' },
        { key: 'vesselNm', label: '航班/船只号' }
    ];
    
    // 检查必填字段
    requiredFields.forEach(({ key, label }) => {
        if (!data[key] || data[key].trim() === '') {
            errors.push(`${label}不能为空`);
        }
    });
    
    // 邮箱格式验证
    if (data.email && !isValidEmail(data.email)) {
        errors.push('邮箱格式不正确');
    }
    
    // 日期格式验证
    if (data.dob && !isValidDate(data.dob)) {
        errors.push('出生日期格式不正确，应为DD/MM/YYYY');
    }
    
    if (data.passExpDte && !isValidDate(data.passExpDte)) {
        errors.push('护照有效期格式不正确，应为DD/MM/YYYY');
    }
    
    if (data.arrDt && !isValidDate(data.arrDt)) {
        errors.push('到达日期格式不正确，应为DD/MM/YYYY');
    }
    
    if (data.depDt && !isValidDate(data.depDt)) {
        errors.push('离开日期格式不正确，应为DD/MM/YYYY');
    }
    
    // 性别验证
    if (data.sex && !['1', '2'].includes(data.sex)) {
        errors.push('性别选择不正确');
    }
    
    const isValid = errors.length === 0;
    
    if (isValid) {
        console.log('✅ 表单数据验证通过');
    } else {
        console.log('❌ 表单数据验证失败:', errors);
    }
    
    return { isValid, errors };
}

/**
 * 保存持久化数据到Chrome存储
 * @param {Object} data - 要保存的数据
 */
export async function savePersistentData(data) {
    try {
        console.log('💾 保存持久化数据...');
        
        const persistentData = {
            [STORAGE_KEYS.PERSISTENT_EMAIL]: data.email || '',
            [STORAGE_KEYS.PERSISTENT_PHONE]: data.mobile || '',
            [STORAGE_KEYS.PHONE_REGION]: data.region || '60',
            [STORAGE_KEYS.LAST_FORM_DATA]: data
        };
        
        await chrome.storage.local.set(persistentData);
        console.log('✅ 持久化数据保存成功');
    } catch (error) {
        console.error('❌ 保存持久化数据失败:', error);
    }
}

/**
 * 加载持久化数据
 * @returns {Promise<Object>} 持久化数据
 */
export async function loadPersistentData() {
    try {
        console.log('📂 加载持久化数据...');
        
        const keys = Object.values(STORAGE_KEYS);
        const result = await chrome.storage.local.get(keys);
        
        console.log('✅ 持久化数据加载成功:', result);
        return result;
    } catch (error) {
        console.error('❌ 加载持久化数据失败:', error);
        return {};
    }
}

/**
 * 应用持久化数据到表单
 * @param {Object} persistentData - 持久化数据
 */
export function applyPersistentData(persistentData) {
    console.log('🔄 应用持久化数据到表单...');
    
    // 应用邮箱
    const emailField = document.getElementById('email');
    if (emailField && persistentData[STORAGE_KEYS.PERSISTENT_EMAIL]) {
        emailField.value = persistentData[STORAGE_KEYS.PERSISTENT_EMAIL];
    }
    
    // 应用手机号码
    const mobileField = document.getElementById('mobile');
    if (mobileField && persistentData[STORAGE_KEYS.PERSISTENT_PHONE]) {
        mobileField.value = persistentData[STORAGE_KEYS.PERSISTENT_PHONE];
    }
    
    // 应用电话区号
    const regionField = document.getElementById('phoneRegion');
    if (regionField && persistentData[STORAGE_KEYS.PHONE_REGION]) {
        regionField.value = persistentData[STORAGE_KEYS.PHONE_REGION];
    }
    
    console.log('✅ 持久化数据应用完成');
}

// 辅助函数
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function isValidDate(dateStr) {
    const dateRegex = /^\d{2}\/\d{2}\/\d{4}$/;
    return dateRegex.test(dateStr);
}
