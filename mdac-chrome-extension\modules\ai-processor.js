// MDAC智能填充助手 - AI处理模块
// 负责使用Gemini API解析旅客信息

import { GEMINI_CONFIG, AI_PROMPT_TEMPLATE, ERROR_MESSAGES } from './config.js';

/**
 * 使用Gemini API提取和解析旅客信息
 * @param {string} travelerInfo - 用户输入的旅客信息
 * @returns {Promise<Object>} 解析后的结构化数据
 */
export async function extractDataWithGemini(travelerInfo) {
    if (!travelerInfo || travelerInfo.trim().length < 10) {
        throw new Error('请输入至少10个字符的旅客信息');
    }

    const prompt = `${AI_PROMPT_TEMPLATE}

旅客信息：
${travelerInfo}

重要提示：
1. 请仔细解析上述实际旅客信息，不要使用示例数据
2. 只返回JSON格式的数据，不要包含任何其他文字或解释
3. 根据实际旅客信息填写字段值，如果某个字段无法从提供的信息中提取，请返回null
4. 确保所有提取的数据都来自于实际的旅客信息，而不是示例或模板数据
5. 姓名必须大写英文
6. 日期格式必须为DD/MM/YYYY
7. 国籍使用3位代码（如CHN、USA、GBR）
8. 性别：男性=1，女性=2
9. 电话区号只填数字（如60、86、1）

请返回JSON格式数据：`;

    try {
        console.log('🤖 开始调用Gemini API解析数据...');
        
        const response = await fetch(`${GEMINI_CONFIG.API_URL}?key=${GEMINI_CONFIG.API_KEY}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                contents: [{
                    parts: [{
                        text: prompt
                    }]
                }],
                generationConfig: {
                    temperature: 0.1,
                    topK: 1,
                    topP: 1,
                    maxOutputTokens: 2048,
                }
            })
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error('❌ Gemini API请求失败:', response.status, errorText);
            throw new Error(`${ERROR_MESSAGES.API_ERROR}: ${response.status}`);
        }

        const data = await response.json();
        
        if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
            console.error('❌ Gemini API返回数据格式错误:', data);
            throw new Error(ERROR_MESSAGES.API_ERROR);
        }
        
        const content = data.candidates[0].content.parts[0].text;
        console.log('🔍 Gemini API原始响应:', content);
        
        // 清理响应内容，提取JSON
        let jsonString = content.trim();
        if (jsonString.startsWith('```json')) {
            jsonString = jsonString.replace(/^```json\s*/, '').replace(/\s*```$/, '');
        } else if (jsonString.startsWith('```')) {
            jsonString = jsonString.replace(/^```\s*/, '').replace(/\s*```$/, '');
        }

        try {
            const parsedData = JSON.parse(jsonString);
            console.log('🔍 AI解析的原始数据:', parsedData);
            
            // 数据验证和清理
            const cleanedData = validateAndCleanData(parsedData);
            console.log('✅ 清理后的数据:', cleanedData);
            return cleanedData;
        } catch (e) {
            console.error('❌ JSON解析失败:', jsonString);
            console.error('❌ 详细错误信息:', e.message);
            throw new Error(`${ERROR_MESSAGES.AI_PARSE_FAILED}: ${e.message}`);
        }
    } catch (error) {
        console.error('❌ AI处理过程出错:', error);
        if (error.message.includes('fetch')) {
            throw new Error(ERROR_MESSAGES.NETWORK_ERROR);
        }
        throw error;
    }
}

/**
 * 数据验证和清理
 * @param {Object} data - 原始解析数据
 * @returns {Object} 清理后的数据
 */
export function validateAndCleanData(data) {
    const cleaned = { ...data };
    
    console.log('🧹 开始数据清理和验证...');
    
    // 姓名处理：确保大写
    if (cleaned.name && typeof cleaned.name === 'string') {
        cleaned.name = cleaned.name.toUpperCase().trim();
    }
    
    // 护照号码处理：确保大写
    if (cleaned.passNo && typeof cleaned.passNo === 'string') {
        cleaned.passNo = cleaned.passNo.toUpperCase().trim();
    }
    
    // 日期格式验证和转换
    cleaned.dob = validateAndFormatDate(cleaned.dob, '出生日期');
    cleaned.passExpDte = validateAndFormatDate(cleaned.passExpDte, '护照有效期');
    cleaned.arrDt = validateAndFormatDate(cleaned.arrDt, '到达日期');
    cleaned.depDt = validateAndFormatDate(cleaned.depDt, '离开日期');
    
    // 国籍代码验证
    if (cleaned.nationality && typeof cleaned.nationality === 'string') {
        cleaned.nationality = cleaned.nationality.toUpperCase().trim();
        if (cleaned.nationality.length !== 3) {
            console.warn('⚠️ 国籍代码格式可能不正确:', cleaned.nationality);
        }
    }
    
    // 性别验证
    if (cleaned.sex && !['1', '2'].includes(String(cleaned.sex))) {
        console.warn('⚠️ 性别代码不正确，应为1或2:', cleaned.sex);
        cleaned.sex = null;
    }
    
    // 邮箱验证
    if (cleaned.email && !isValidEmail(cleaned.email)) {
        console.warn('⚠️ 邮箱格式不正确:', cleaned.email);
        cleaned.email = null;
    }
    
    // 确认邮箱设置为与邮箱相同
    if (cleaned.email && !cleaned.confirmEmail) {
        cleaned.confirmEmail = cleaned.email;
    }
    
    // 电话区号验证
    if (cleaned.region && typeof cleaned.region === 'string') {
        cleaned.region = cleaned.region.replace(/^\+/, '').trim();
    }
    
    // 手机号码清理
    if (cleaned.mobile && typeof cleaned.mobile === 'string') {
        cleaned.mobile = cleaned.mobile.replace(/[^\d]/g, '');
    }
    
    // 航班号处理
    if (cleaned.vesselNm && typeof cleaned.vesselNm === 'string') {
        cleaned.vesselNm = cleaned.vesselNm.toUpperCase().trim();
    }
    
    // 交通方式验证
    if (cleaned.trvlMode && !['1', '2', '3'].includes(String(cleaned.trvlMode))) {
        console.warn('⚠️ 交通方式代码不正确，应为1、2或3:', cleaned.trvlMode);
        cleaned.trvlMode = '1'; // 默认为飞机
    }
    
    // 邮政编码处理
    if (cleaned.accommodationPostcode && typeof cleaned.accommodationPostcode === 'string') {
        cleaned.accommodationPostcode = cleaned.accommodationPostcode.replace(/[^\d]/g, '');
    }
    
    console.log('✅ 数据清理完成');
    return cleaned;
}

/**
 * 验证和格式化日期
 * @param {string} dateStr - 日期字符串
 * @param {string} fieldName - 字段名称（用于错误提示）
 * @returns {string|null} 格式化后的日期或null
 */
function validateAndFormatDate(dateStr, fieldName) {
    if (!dateStr) return null;
    
    try {
        // 尝试解析各种日期格式
        let date;
        
        if (dateStr.includes('/')) {
            // DD/MM/YYYY 或 MM/DD/YYYY 或 YYYY/MM/DD
            const parts = dateStr.split('/');
            if (parts.length === 3) {
                if (parts[0].length === 4) {
                    // YYYY/MM/DD
                    date = new Date(parts[0], parts[1] - 1, parts[2]);
                } else if (parts[2].length === 4) {
                    // DD/MM/YYYY 或 MM/DD/YYYY
                    // 假设是DD/MM/YYYY格式
                    date = new Date(parts[2], parts[1] - 1, parts[0]);
                }
            }
        } else if (dateStr.includes('-')) {
            // YYYY-MM-DD 或 DD-MM-YYYY
            const parts = dateStr.split('-');
            if (parts.length === 3) {
                if (parts[0].length === 4) {
                    // YYYY-MM-DD
                    date = new Date(parts[0], parts[1] - 1, parts[2]);
                } else {
                    // DD-MM-YYYY
                    date = new Date(parts[2], parts[1] - 1, parts[0]);
                }
            }
        } else {
            // 尝试直接解析
            date = new Date(dateStr);
        }
        
        if (date && !isNaN(date.getTime())) {
            // 格式化为DD/MM/YYYY
            const day = String(date.getDate()).padStart(2, '0');
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const year = date.getFullYear();
            return `${day}/${month}/${year}`;
        } else {
            console.warn(`⚠️ ${fieldName}日期格式不正确:`, dateStr);
            return null;
        }
    } catch (error) {
        console.warn(`⚠️ ${fieldName}日期解析失败:`, dateStr, error.message);
        return null;
    }
}

/**
 * 验证邮箱格式
 * @param {string} email - 邮箱地址
 * @returns {boolean} 是否有效
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * 自动解析设置 - 监听输入变化并自动触发解析
 * @param {HTMLElement} textarea - 输入框元素
 * @param {Function} callback - 解析完成回调函数
 */
export function setupAutoAnalysis(textarea, callback) {
    let timeout;
    let lastValue = '';
    
    const indicator = document.getElementById('autoParseIndicator');
    
    function handleInput() {
        const currentValue = textarea.value.trim();
        
        // 清除之前的定时器
        if (timeout) {
            clearTimeout(timeout);
        }
        
        // 如果内容没有变化或太短，不处理
        if (currentValue === lastValue || currentValue.length <= 10) {
            if (indicator) {
                indicator.classList.remove('active');
            }
            return;
        }
        
        // 显示自动解析指示器
        if (indicator) {
            indicator.classList.add('active');
        }
        
        // 设置1秒延迟自动解析
        timeout = setTimeout(async () => {
            if (indicator) {
                indicator.classList.remove('active');
            }
            
            if (currentValue.length > 10 && currentValue !== lastValue) {
                lastValue = currentValue;
                try {
                    console.log('🚀 自动触发AI解析...');
                    await callback(currentValue);
                } catch (error) {
                    console.error('❌ 自动解析失败:', error);
                }
            }
        }, 1000);
    }
    
    // 监听输入事件
    textarea.addEventListener('input', handleInput);
    textarea.addEventListener('paste', () => {
        setTimeout(handleInput, 100); // 延迟处理粘贴内容
    });
    
    console.log('✅ 自动解析功能已启用');
}
