// MDAC智能填充助手 - 扩展功能测试脚本
// 用于在MDAC网站上测试扩展的各项功能

console.log('🧪 MDAC扩展功能测试开始...');

// 测试数据
const testData = {
    name: "LI MING",
    passNo: "G12345678",
    dob: "01/01/1990",
    nationality: "CHN",
    sex: "1",
    passExpDte: "01/01/2030",
    email: "<EMAIL>",
    confirmEmail: "<EMAIL>",
    region: "60",
    mobile: "123456789",
    arrDt: "01/12/2024",
    depDt: "05/12/2024",
    vesselNm: "MH123",
    trvlMode: "1",
    embark: "CHN",
    accommodationStay: "01",
    accommodationAddress1: "Hotel KL City Center",
    accommodationState: "14",
    accommodationPostcode: "50000"
};

// 测试函数集合
const tests = {
    // 测试1: 页面检测
    async testPageDetection() {
        console.log('🔍 测试1: 页面检测');
        
        const requiredFields = ['#name', '#passNo', '#nationality', '#email'];
        const results = {};
        
        requiredFields.forEach(selector => {
            const element = document.querySelector(selector);
            results[selector] = {
                exists: !!element,
                type: element ? element.tagName.toLowerCase() : null,
                id: element ? element.id : null
            };
        });
        
        console.log('📋 字段检测结果:', results);
        
        const existingCount = Object.values(results).filter(r => r.exists).length;
        const success = existingCount >= 3;
        
        console.log(success ? '✅ 页面检测通过' : '❌ 页面检测失败');
        return { success, results, existingCount };
    },
    
    // 测试2: 基础字段填充
    async testBasicFieldFilling() {
        console.log('📝 测试2: 基础字段填充');
        
        const basicFields = [
            { selector: '#name', value: testData.name },
            { selector: '#passNo', value: testData.passNo },
            { selector: '#dob', value: testData.dob },
            { selector: '#email', value: testData.email },
            { selector: '#mobile', value: testData.mobile }
        ];
        
        let successCount = 0;
        const results = [];
        
        for (const field of basicFields) {
            try {
                const element = document.querySelector(field.selector);
                if (element) {
                    element.value = field.value;
                    element.dispatchEvent(new Event('input', { bubbles: true }));
                    element.dispatchEvent(new Event('change', { bubbles: true }));
                    
                    const success = element.value === field.value;
                    if (success) successCount++;
                    
                    results.push({
                        selector: field.selector,
                        expected: field.value,
                        actual: element.value,
                        success
                    });
                    
                    console.log(`${success ? '✅' : '❌'} ${field.selector}: ${field.value}`);
                } else {
                    results.push({
                        selector: field.selector,
                        expected: field.value,
                        actual: null,
                        success: false,
                        error: '字段不存在'
                    });
                    console.log(`❌ ${field.selector}: 字段不存在`);
                }
            } catch (error) {
                results.push({
                    selector: field.selector,
                    expected: field.value,
                    actual: null,
                    success: false,
                    error: error.message
                });
                console.log(`❌ ${field.selector}: ${error.message}`);
            }
        }
        
        console.log(`📊 基础字段填充结果: ${successCount}/${basicFields.length}`);
        return { successCount, totalFields: basicFields.length, results };
    },
    
    // 测试3: 下拉菜单填充
    async testSelectFieldFilling() {
        console.log('🔽 测试3: 下拉菜单填充');
        
        const selectFields = [
            { selector: '#nationality', value: testData.nationality },
            { selector: '#sex', value: testData.sex },
            { selector: '#region', value: testData.region },
            { selector: '#trvlMode', value: testData.trvlMode }
        ];
        
        let successCount = 0;
        const results = [];
        
        for (const field of selectFields) {
            try {
                const element = document.querySelector(field.selector);
                if (element && element.tagName === 'SELECT') {
                    // 查找对应的选项
                    const option = element.querySelector(`option[value="${field.value}"]`);
                    if (option) {
                        element.value = field.value;
                        option.selected = true;
                        element.dispatchEvent(new Event('change', { bubbles: true }));
                        
                        const success = element.value === field.value;
                        if (success) successCount++;
                        
                        results.push({
                            selector: field.selector,
                            expected: field.value,
                            actual: element.value,
                            optionText: option.text,
                            success
                        });
                        
                        console.log(`${success ? '✅' : '❌'} ${field.selector}: ${field.value} (${option.text})`);
                    } else {
                        results.push({
                            selector: field.selector,
                            expected: field.value,
                            actual: element.value,
                            success: false,
                            error: `未找到值为 "${field.value}" 的选项`
                        });
                        console.log(`❌ ${field.selector}: 未找到选项 "${field.value}"`);
                    }
                } else {
                    results.push({
                        selector: field.selector,
                        expected: field.value,
                        actual: null,
                        success: false,
                        error: element ? '不是下拉菜单' : '字段不存在'
                    });
                    console.log(`❌ ${field.selector}: ${element ? '不是下拉菜单' : '字段不存在'}`);
                }
            } catch (error) {
                results.push({
                    selector: field.selector,
                    expected: field.value,
                    actual: null,
                    success: false,
                    error: error.message
                });
                console.log(`❌ ${field.selector}: ${error.message}`);
            }
        }
        
        console.log(`📊 下拉菜单填充结果: ${successCount}/${selectFields.length}`);
        return { successCount, totalFields: selectFields.length, results };
    },
    
    // 测试4: 字段映射验证
    async testFieldMapping() {
        console.log('🗺️ 测试4: 字段映射验证');
        
        const fieldMappings = [
            { scriptField: 'phoneRegion', actualField: 'region' },
            { scriptField: 'region', actualField: 'region' }
        ];
        
        const results = [];
        
        fieldMappings.forEach(mapping => {
            const scriptElement = document.querySelector(`#${mapping.scriptField}`);
            const actualElement = document.querySelector(`#${mapping.actualField}`);
            
            const result = {
                scriptField: mapping.scriptField,
                actualField: mapping.actualField,
                scriptExists: !!scriptElement,
                actualExists: !!actualElement,
                mappingCorrect: !scriptElement && !!actualElement
            };
            
            results.push(result);
            
            console.log(`${result.mappingCorrect ? '✅' : '⚠️'} ${mapping.scriptField} → ${mapping.actualField}`);
            console.log(`  脚本字段存在: ${result.scriptExists}, 实际字段存在: ${result.actualExists}`);
        });
        
        return { results };
    },
    
    // 测试5: 事件触发验证
    async testEventTriggering() {
        console.log('⚡ 测试5: 事件触发验证');
        
        const testField = document.querySelector('#name');
        if (!testField) {
            console.log('❌ 测试字段不存在');
            return { success: false, error: '测试字段不存在' };
        }
        
        let eventsFired = [];
        
        // 监听事件
        ['input', 'change', 'blur', 'focus'].forEach(eventType => {
            testField.addEventListener(eventType, () => {
                eventsFired.push(eventType);
            });
        });
        
        // 触发事件
        testField.focus();
        testField.value = 'TEST VALUE';
        testField.dispatchEvent(new Event('input', { bubbles: true }));
        testField.dispatchEvent(new Event('change', { bubbles: true }));
        testField.blur();
        
        console.log('🔥 触发的事件:', eventsFired);
        
        const expectedEvents = ['focus', 'input', 'change', 'blur'];
        const success = expectedEvents.every(event => eventsFired.includes(event));
        
        console.log(success ? '✅ 事件触发正常' : '⚠️ 部分事件未触发');
        return { success, eventsFired, expectedEvents };
    },
    
    // 测试6: 完整表单填充
    async testCompleteFormFilling() {
        console.log('📋 测试6: 完整表单填充');
        
        let totalFields = 0;
        let successCount = 0;
        const errors = [];
        
        // 填充所有字段
        for (const [key, value] of Object.entries(testData)) {
            if (!value) continue;
            
            const selector = `#${key}`;
            const element = document.querySelector(selector);
            
            totalFields++;
            
            if (element) {
                try {
                    if (element.tagName === 'SELECT') {
                        const option = element.querySelector(`option[value="${value}"]`);
                        if (option) {
                            element.value = value;
                            option.selected = true;
                            element.dispatchEvent(new Event('change', { bubbles: true }));
                            successCount++;
                            console.log(`✅ ${selector}: ${value}`);
                        } else {
                            errors.push(`${selector}: 未找到选项 "${value}"`);
                            console.log(`❌ ${selector}: 未找到选项 "${value}"`);
                        }
                    } else {
                        element.value = value;
                        element.dispatchEvent(new Event('input', { bubbles: true }));
                        element.dispatchEvent(new Event('change', { bubbles: true }));
                        successCount++;
                        console.log(`✅ ${selector}: ${value}`);
                    }
                } catch (error) {
                    errors.push(`${selector}: ${error.message}`);
                    console.log(`❌ ${selector}: ${error.message}`);
                }
            } else {
                errors.push(`${selector}: 字段不存在`);
                console.log(`⚠️ ${selector}: 字段不存在`);
            }
        }
        
        console.log(`📊 完整表单填充结果: ${successCount}/${totalFields}`);
        if (errors.length > 0) {
            console.log('❌ 错误列表:', errors);
        }
        
        return { successCount, totalFields, errors };
    }
};

// 运行所有测试
async function runAllTests() {
    console.log('🚀 开始运行所有测试...');
    
    const results = {};
    
    for (const [testName, testFunc] of Object.entries(tests)) {
        try {
            console.log(`\n--- ${testName} ---`);
            results[testName] = await testFunc();
        } catch (error) {
            console.error(`❌ 测试 ${testName} 失败:`, error);
            results[testName] = { success: false, error: error.message };
        }
    }
    
    console.log('\n📊 测试总结:');
    console.log('='.repeat(50));
    
    Object.entries(results).forEach(([testName, result]) => {
        const status = result.success !== false ? '✅' : '❌';
        console.log(`${status} ${testName}`);
        
        if (result.successCount !== undefined && result.totalFields !== undefined) {
            console.log(`   成功率: ${result.successCount}/${result.totalFields}`);
        }
        
        if (result.error) {
            console.log(`   错误: ${result.error}`);
        }
    });
    
    console.log('='.repeat(50));
    console.log('🏁 测试完成');
    
    return results;
}

// 导出测试函数
if (typeof window !== 'undefined') {
    window.mdacTests = {
        runAllTests,
        tests,
        testData
    };
}

// 如果在控制台中直接运行，自动开始测试
if (typeof window !== 'undefined' && window.location.href.includes('imigresen-online.imi.gov.my/mdac')) {
    console.log('🎯 检测到MDAC页面，可以运行测试');
    console.log('💡 使用方法: window.mdacTests.runAllTests()');
}
