// MDAC智能填充助手 - MDAC填充核心模块
// 负责直接在MDAC页面执行表单填充操作

import { FIELD_MAPPING, ACTUAL_FIELD_MAPPING, ERROR_MESSAGES, SUCCESS_MESSAGES } from './config.js';

/**
 * 检测当前页面是否为MDAC网站
 * @returns {Promise<boolean>} 是否为MDAC页面
 */
export async function detectMDACPage() {
    try {
        const [tab] = await chrome.tabs.query({active: true, currentWindow: true});
        
        if (!tab.url.includes('imigresen-online.imi.gov.my/mdac')) {
            throw new Error(ERROR_MESSAGES.NOT_MDAC_PAGE);
        }
        
        // 检测必要的表单字段是否存在
        const result = await chrome.scripting.executeScript({
            target: {tabId: tab.id},
            func: () => {
                const requiredFields = ['#name', '#passNo', '#nationality', '#email'];
                const existingFields = requiredFields.filter(selector => 
                    document.querySelector(selector) !== null
                );
                
                return {
                    hasRequiredFields: existingFields.length >= 3,
                    existingFields: existingFields,
                    pageTitle: document.title,
                    url: window.location.href
                };
            }
        });
        
        const pageInfo = result[0].result;
        
        if (!pageInfo.hasRequiredFields) {
            throw new Error(ERROR_MESSAGES.FORM_NOT_LOADED);
        }
        
        console.log('✅ MDAC页面检测成功:', pageInfo);
        return { success: true, tabId: tab.id, pageInfo };
        
    } catch (error) {
        console.error('❌ MDAC页面检测失败:', error);
        throw error;
    }
}

/**
 * 执行MDAC表单自动填充
 * @param {Object} formData - 表单数据
 * @returns {Promise<Object>} 执行结果
 */
export async function executeAutoFill(formData) {
    try {
        console.log('🚀 开始执行MDAC表单自动填充...');
        
        // 检测页面
        const { tabId } = await detectMDACPage();
        
        // 注入并执行填充脚本
        const result = await chrome.scripting.executeScript({
            target: {tabId: tabId},
            func: fillMDACForm,
            args: [formData]
        });
        
        const fillResult = result[0].result;
        
        if (fillResult.success) {
            console.log('✅ MDAC表单填充完成:', fillResult);
            return fillResult;
        } else {
            throw new Error(fillResult.error || ERROR_MESSAGES.FILL_FAILED);
        }
        
    } catch (error) {
        console.error('❌ MDAC表单填充失败:', error);
        throw error;
    }
}

/**
 * 注入到MDAC页面的填充函数
 * 这个函数会在MDAC页面的上下文中执行
 * @param {Object} formData - 表单数据
 * @returns {Object} 执行结果
 */
function fillMDACForm(formData) {
    console.log('🤖 MDAC表单自动填充脚本开始执行...');
    console.log('📊 表单数据:', formData);
    
    let successCount = 0;
    let totalFields = 0;
    const errors = [];
    
    try {
        // 🔧 填充函数
        function fillField(selector, value, isSelect = false) {
            totalFields++;
            const element = document.querySelector(selector);
            
            if (element && value !== undefined && value !== null && value !== '') {
                try {
                    if (isSelect && element.tagName === 'SELECT') {
                        return fillSelectField(element, value, selector);
                    } else {
                        element.value = value;
                    }
                    
                    // 触发事件以确保表单验证
                    element.dispatchEvent(new Event('input', { bubbles: true }));
                    element.dispatchEvent(new Event('change', { bubbles: true }));
                    element.dispatchEvent(new Event('blur', { bubbles: true }));
                    
                    successCount++;
                    console.log(`✅ 填充 ${selector}: ${value}`);
                    return true;
                } catch (error) {
                    console.error(`❌ 填充失败 ${selector}:`, error);
                    errors.push(`填充失败 ${selector}: ${error.message}`);
                    return false;
                }
            } else {
                console.warn(`⚠️ 字段不存在或值为空: ${selector}`);
                return false;
            }
        }
        
        // 专门处理下拉菜单的函数
        function fillSelectField(selectElement, value, selector) {
            try {
                // 1. 移除所有现有的selected属性
                Array.from(selectElement.options).forEach(option => {
                    option.removeAttribute('selected');
                });
                
                // 2. 设置新的值
                selectElement.value = value;
                
                // 3. 找到对应的选项并设置selected属性
                const targetOption = selectElement.querySelector(`option[value="${value}"]`);
                if (targetOption) {
                    targetOption.setAttribute('selected', '');
                    selectElement.selectedIndex = Array.from(selectElement.options).indexOf(targetOption);
                } else {
                    console.warn(`⚠️ 未找到值为 "${value}" 的选项在 ${selector}`);
                    return false;
                }
                
                // 4. 触发所有必要的事件
                const events = ['focus', 'input', 'change', 'blur'];
                events.forEach(eventType => {
                    const event = new Event(eventType, { bubbles: true, cancelable: true });
                    selectElement.dispatchEvent(event);
                });
                
                // 5. 验证设置是否成功
                if (selectElement.value === value) {
                    successCount++;
                    console.log(`✅ 下拉菜单填充成功 ${selector}: ${value} (${targetOption ? targetOption.text : '未知'})`);
                    return true;
                } else {
                    console.error(`❌ 下拉菜单填充失败 ${selector}: 期望值 ${value}, 实际值 ${selectElement.value}`);
                    return false;
                }
            } catch (error) {
                console.error(`❌ 下拉菜单填充异常 ${selector}:`, error);
                errors.push(`下拉菜单填充异常 ${selector}: ${error.message}`);
                return false;
            }
        }
        
        // ⏰ 等待函数
        function wait(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }
        
        // 🔄 重试机制的下拉菜单填充函数
        async function fillFieldWithRetry(selector, value, isSelect = false, maxRetries = 3, delay = 500) {
            for (let attempt = 1; attempt <= maxRetries; attempt++) {
                console.log(`🔄 尝试填充 ${selector}, 第 ${attempt}/${maxRetries} 次`);
                
                const success = fillField(selector, value, isSelect);
                
                if (success) {
                    console.log(`✅ 成功填充 ${selector} (第 ${attempt} 次尝试)`);
                    return true;
                }
                
                if (attempt < maxRetries) {
                    console.log(`⏳ 等待 ${delay}ms 后重试...`);
                    await wait(delay);
                    delay *= 1.5;
                } else {
                    console.error(`❌ 填充失败 ${selector}, 已达到最大重试次数 ${maxRetries}`);
                    errors.push(`填充失败 ${selector}: 达到最大重试次数`);
                    return false;
                }
            }
            return false;
        }
        
        // 🎯 选择性字段更新函数 - 只更新非空字段
        function fillFieldSelectively(selector, value, isSelect = false, fieldName = '') {
            if (value === null || value === undefined || value === '') {
                console.log(`⏸️ 跳过空字段 ${fieldName} (${selector})`);
                return false;
            }
            
            return fillField(selector, value, isSelect);
        }
        
        // 获取实际字段ID（处理字段映射差异）
        function getActualFieldId(scriptFieldId) {
            const mapping = {
                'phoneRegion': 'region',
                'region': 'region'
            };
            return mapping[scriptFieldId] || scriptFieldId;
        }

        // 🏙️ 改进的城市选择函数，支持州属变化后的城市加载
        async function selectCityWithStateLoad(stateCode, addressLine1, maxWaitTime = 5000) {
            console.log(`🏛️ 开始处理州属 ${stateCode} 的城市选择`);

            // 等待城市下拉菜单加载
            const startTime = Date.now();
            while (Date.now() - startTime < maxWaitTime) {
                const citySelect = document.querySelector('#accommodationCity');
                if (citySelect && citySelect.options.length > 1) {
                    console.log(`🏙️ 城市列表已加载，共 ${citySelect.options.length - 1} 个选项`);
                    return selectCity(stateCode, addressLine1);
                }

                console.log('⏳ 等待城市列表加载...');
                await wait(500);
            }

            console.warn(`⚠️ 城市列表加载超时 (${maxWaitTime}ms)`);
            return false;
        }

        // 🎯 智能城市选择 - 基于地址关键词匹配
        function selectCity(stateCode, addressLine1) {
            const citySelect = document.querySelector('#accommodationCity');
            if (!citySelect || citySelect.options.length <= 1) {
                console.log('🏙️ 城市列表未加载，等待重试...');
                return false;
            }

            if (!addressLine1) {
                console.log('⚠️ 没有地址信息，无法智能选择城市');
                return false;
            }

            const addressLower = addressLine1.toLowerCase();
            console.log(`🔍 分析地址关键词: "${addressLine1}"`);

            // 城市关键词映射
            const cityKeywords = getCityKeywords(stateCode);

            // 查找匹配的城市
            let selectedCity = null;
            let matchedKeyword = '';

            for (const [keyword, cityName] of Object.entries(cityKeywords)) {
                if (addressLower.includes(keyword.toLowerCase())) {
                    // 在下拉菜单中查找对应的城市选项
                    for (let i = 0; i < citySelect.options.length; i++) {
                        const option = citySelect.options[i];
                        if (option.text.toUpperCase().includes(cityName.toUpperCase()) ||
                            option.value.toUpperCase().includes(cityName.toUpperCase())) {
                            selectedCity = option.value;
                            matchedKeyword = keyword;
                            break;
                        }
                    }
                    if (selectedCity) break;
                }
            }

            if (selectedCity) {
                const success = fillField('#accommodationCity', selectedCity, true);
                if (success) {
                    console.log(`✅ 智能城市选择成功: ${matchedKeyword} → ${selectedCity}`);
                    return true;
                } else {
                    console.error(`❌ 城市选择失败: ${selectedCity}`);
                    return false;
                }
            } else {
                console.log(`⚠️ 未找到匹配的城市关键词，地址: "${addressLine1}"`);
                console.log('📋 可用城市选项:');
                for (let i = 1; i < Math.min(citySelect.options.length, 6); i++) {
                    console.log(`  - ${citySelect.options[i].text} (${citySelect.options[i].value})`);
                }
                return false;
            }
        }

        // 获取州属对应的城市关键词映射
        function getCityKeywords(stateCode) {
            const cityMappings = {
                '01': { // 柔佛州
                    'johor bahru': 'JOHOR BAHRU', 'jb': 'JOHOR BAHRU', 'legoland': 'JOHOR BAHRU',
                    'nusajaya': 'JOHOR BAHRU', 'iskandar': 'ISKANDAR PUTERI', 'bukit indah': 'JOHOR BAHRU',
                    'skudai': 'SKUDAI', 'masai': 'MASAI', 'pasir gudang': 'PASIR GUDANG',
                    'kluang': 'KLUANG', 'batu pahat': 'BATU PAHAT', 'muar': 'MUAR',
                    'pontian': 'PONTIAN', 'segamat': 'SEGAMAT', 'mersing': 'MERSING',
                    'kota tinggi': 'KOTA TINGGI', 'kulai': 'KULAI', 'senai': 'SENAI'
                },
                '02': { // 吉打州
                    'alor setar': 'ALOR SETAR', 'alor star': 'ALOR SETAR',
                    'sungai petani': 'SUNGAI PETANI', 'kulim': 'KULIM', 'baling': 'BALING',
                    'langkawi': 'LANGKAWI', 'jitra': 'JITRA', 'kuala kedah': 'KUALA KEDAH'
                },
                '03': { // 吉兰丹州
                    'kota bharu': 'KOTA BHARU', 'kota bahru': 'KOTA BHARU', 'kb': 'KOTA BHARU',
                    'bachok': 'BACHOK', 'pasir mas': 'PASIR MAS', 'tumpat': 'TUMPAT',
                    'kuala krai': 'KUALA KRAI', 'gua musang': 'GUA MUSANG', 'machang': 'MACHANG'
                },
                '04': { // 马六甲州
                    'melaka': 'MELAKA', 'malacca': 'MELAKA', 'alor gajah': 'ALOR GAJAH',
                    'jasin': 'JASIN', 'masjid tanah': 'MASJID TANAH', 'merlimau': 'MERLIMAU'
                },
                '05': { // 森美兰州
                    'seremban': 'SEREMBAN', 'port dickson': 'PORT DICKSON', 'pd': 'PORT DICKSON',
                    'nilai': 'NILAI', 'bahau': 'BAHAU', 'kuala pilah': 'KUALA PILAH',
                    'rembau': 'REMBAU', 'tampin': 'TAMPIN'
                },
                '06': { // 彭亨州
                    'kuantan': 'KUANTAN', 'temerloh': 'TEMERLOH', 'bentong': 'BENTONG',
                    'raub': 'RAUB', 'jerantut': 'JERANTUT', 'pekan': 'PEKAN',
                    'cameron highlands': 'C HIGHLANDS', 'genting': 'GENTING'
                },
                '07': { // 槟城州
                    'georgetown': 'GEORGETOWN', 'penang': 'GEORGETOWN', 'bukit mertajam': 'BUKIT MERTAJAM',
                    'butterworth': 'BUTTERWORTH', 'bayan lepas': 'BAYAN LEPAS', 'bayan baru': 'BAYAN BARU',
                    'ayer itam': 'AYER ITAM', 'balik pulau': 'BALIK PULAU', 'batu ferringhi': 'BATU FERRINGHI'
                },
                '08': { // 霹雳州
                    'ipoh': 'IPOH', 'taiping': 'TAIPING', 'teluk intan': 'TELUK INTAN',
                    'kampar': 'KAMPAR', 'batu gajah': 'BATU GAJAH', 'kuala kangsar': 'KUALA KANGSAR',
                    'lumut': 'LUMUT', 'sitiawan': 'SITIAWAN', 'pangkor': 'PANGKOR'
                },
                '09': { // 玻璃市州
                    'kangar': 'KANGAR', 'arau': 'ARAU', 'kuala perlis': 'KUALA PERLIS',
                    'padang besar': 'PADANG BESAR', 'perlis': 'KANGAR'
                },
                '10': { // 雪兰莪州
                    'shah alam': 'SHAH ALAM', 'petaling jaya': 'PETALING JAYA', 'pj': 'PETALING JAYA',
                    'subang jaya': 'SUBANG JAYA', 'subang': 'SUBANG JAYA', 'klang': 'KLANG',
                    'kajang': 'KAJANG', 'ampang': 'AMPANG', 'puchong': 'PUCHONG',
                    'cyberjaya': 'CYBERJAYA', 'sepang': 'SEPANG', 'rawang': 'RAWANG'
                },
                '11': { // 登嘉楼州
                    'kuala terengganu': 'KUALA TERENGGANU', 'kt': 'KUALA TERENGGANU',
                    'kemaman': 'KEMAMAN', 'dungun': 'DUNGUN', 'marang': 'MARANG'
                },
                '12': { // 沙巴州
                    'kota kinabalu': 'KOTA KINABALU', 'kk': 'KOTA KINABALU',
                    'sandakan': 'SANDAKAN', 'tawau': 'TAWAU', 'lahad datu': 'LAHAD DATU'
                },
                '13': { // 砂拉越州
                    'kuching': 'KUCHING', 'miri': 'MIRI', 'sibu': 'SIBU',
                    'bintulu': 'BINTULU', 'limbang': 'LIMBANG'
                },
                '14': { // 吉隆坡
                    'kuala lumpur': 'KUALA LUMPUR', 'kl': 'KUALA LUMPUR', 'klcc': 'KUALA LUMPUR',
                    'bukit bintang': 'KUALA LUMPUR', 'cheras': 'CHERAS', 'setapak': 'SETAPAK',
                    'wangsa maju': 'WANGSA MAJU', 'kepong': 'KEPONG'
                },
                '15': { // 纳闽
                    'labuan': 'LABUAN'
                },
                '16': { // 布城
                    'putrajaya': 'PUTRAJAYA'
                }
            };

            return cityMappings[stateCode] || {};
        }
        
        // 🚀 异步填充表单
        async function fillForm() {
            try {
                console.log('📋 开始填充个人信息...');
                
                // 基本信息填充
                fillFieldSelectively('#name', formData.name, false, '姓名');
                fillFieldSelectively('#passNo', formData.passNo, false, '护照号码');
                fillFieldSelectively('#dob', formData.dob, false, '出生日期');
                fillFieldSelectively('#nationality', formData.nationality, true, '国籍');
                fillFieldSelectively('#sex', formData.sex, true, '性别');
                fillFieldSelectively('#passExpDte', formData.passExpDte, false, '护照有效期');
                
                // 联系信息填充
                fillFieldSelectively('#email', formData.email, false, '邮箱');
                fillFieldSelectively('#confirmEmail', formData.confirmEmail || formData.email, false, '确认邮箱');
                
                // 电话信息填充（注意字段映射）
                const actualRegionField = getActualFieldId('region');
                fillFieldSelectively(`#${actualRegionField}`, formData.region, true, '电话区号');
                fillFieldSelectively('#mobile', formData.mobile, false, '手机号码');
                
                // 行程信息填充
                fillFieldSelectively('#arrDt', formData.arrDt, false, '到达日期');
                fillFieldSelectively('#depDt', formData.depDt, false, '离开日期');
                fillFieldSelectively('#vesselNm', formData.vesselNm, false, '航班号');
                fillFieldSelectively('#trvlMode', formData.trvlMode || '1', true, '交通方式');
                
                // 其他信息填充
                fillFieldSelectively('#embark', formData.embark || formData.nationality, true, '登船港');
                
                // 住宿信息填充
                if (formData.accommodationState) {
                    console.log('🏨 开始填充住宿信息...');
                    
                    // 住宿类型
                    fillFieldSelectively('#accommodationStay', formData.accommodationStay || '01', true, '住宿类型');
                    
                    // 州属选择
                    const stateSuccess = fillFieldSelectively('#accommodationState', formData.accommodationState, true, '州属');
                    
                    if (stateSuccess) {
                        // 等待城市列表加载
                        console.log('⏳ 等待城市列表加载...');
                        await wait(3000);
                        
                        // 尝试智能选择城市
                        if (formData.accommodationAddress1) {
                            await selectCityWithStateLoad(formData.accommodationState, formData.accommodationAddress1);
                        }
                    }
                    
                    // 地址信息
                    fillFieldSelectively('#accommodationAddress1', formData.accommodationAddress1, false, '地址1');
                    fillFieldSelectively('#accommodationAddress2', formData.accommodationAddress2, false, '地址2');
                    fillFieldSelectively('#accommodationPostcode', formData.accommodationPostcode, false, '邮政编码');
                }
                
                console.log('✅ 表单填充完成');
                
            } catch (error) {
                console.error('❌ 填充过程出错:', error);
                errors.push(`填充过程出错: ${error.message}`);
            }
        }
        
        // 执行填充
        return fillForm().then(() => {
            const result = {
                success: true,
                message: `表单填充完成！成功填充 ${successCount}/${totalFields} 个字段`,
                successCount,
                totalFields,
                errors: errors.length > 0 ? errors : null
            };
            
            console.log('📊 填充统计:', result);
            return result;
        });
        
    } catch (error) {
        console.error('❌ 脚本执行失败:', error);
        return {
            success: false,
            error: error.message,
            successCount,
            totalFields,
            errors
        };
    }
}
