// MDAC智能填充助手 - 后台服务脚本
// 处理扩展生命周期事件和跨标签页通信

console.log('🚀 MDAC智能填充助手后台服务启动');

// 扩展安装或更新时的处理
chrome.runtime.onInstalled.addListener((details) => {
    console.log('📦 扩展安装/更新事件:', details);
    
    if (details.reason === 'install') {
        console.log('🎉 首次安装MDAC智能填充助手');
        
        // 设置默认配置
        chrome.storage.local.set({
            'first_install': true,
            'install_time': Date.now(),
            'version': chrome.runtime.getManifest().version
        });
        
        // 可选：打开欢迎页面或设置页面
        // chrome.tabs.create({ url: 'welcome.html' });
        
    } else if (details.reason === 'update') {
        console.log('🔄 扩展更新完成');
        
        // 更新版本信息
        chrome.storage.local.set({
            'last_update': Date.now(),
            'version': chrome.runtime.getManifest().version
        });
    }
});

// 扩展启动时的处理
chrome.runtime.onStartup.addListener(() => {
    console.log('⚡ 扩展启动');
});

// 处理来自popup的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log('📨 收到消息:', message, '来自:', sender);
    
    switch (message.type) {
        case 'DETECT_MDAC_PAGE':
            handleDetectMDACPage(sendResponse);
            return true; // 保持消息通道开放
            
        case 'EXECUTE_AUTO_FILL':
            handleExecuteAutoFill(message.data, sendResponse);
            return true; // 保持消息通道开放
            
        case 'SAVE_FORM_DATA':
            handleSaveFormData(message.data, sendResponse);
            return true; // 保持消息通道开放
            
        case 'LOAD_FORM_DATA':
            handleLoadFormData(sendResponse);
            return true; // 保持消息通道开放
            
        case 'GET_EXTENSION_INFO':
            handleGetExtensionInfo(sendResponse);
            return true; // 保持消息通道开放
            
        default:
            console.warn('⚠️ 未知消息类型:', message.type);
            sendResponse({ success: false, error: '未知消息类型' });
    }
});

// 处理页面检测请求
async function handleDetectMDACPage(sendResponse) {
    try {
        console.log('🔍 开始检测MDAC页面...');
        
        const [tab] = await chrome.tabs.query({active: true, currentWindow: true});
        
        if (!tab) {
            throw new Error('无法获取当前标签页');
        }
        
        console.log('📄 当前页面:', tab.url);
        
        // 检查URL是否为MDAC网站
        if (!tab.url.includes('imigresen-online.imi.gov.my/mdac')) {
            sendResponse({
                success: false,
                error: '请先打开MDAC网站页面',
                currentUrl: tab.url
            });
            return;
        }
        
        // 检测页面中的表单字段
        const result = await chrome.scripting.executeScript({
            target: {tabId: tab.id},
            func: () => {
                const requiredFields = ['#name', '#passNo', '#nationality', '#email'];
                const existingFields = [];
                const fieldInfo = {};
                
                requiredFields.forEach(selector => {
                    const element = document.querySelector(selector);
                    if (element) {
                        existingFields.push(selector);
                        fieldInfo[selector] = {
                            exists: true,
                            type: element.tagName.toLowerCase(),
                            value: element.value || ''
                        };
                    } else {
                        fieldInfo[selector] = { exists: false };
                    }
                });
                
                return {
                    hasRequiredFields: existingFields.length >= 3,
                    existingFields: existingFields,
                    fieldInfo: fieldInfo,
                    pageTitle: document.title,
                    url: window.location.href,
                    formExists: !!document.querySelector('form')
                };
            }
        });
        
        const pageInfo = result[0].result;
        
        if (!pageInfo.hasRequiredFields) {
            sendResponse({
                success: false,
                error: 'MDAC表单未正确加载，请刷新页面',
                pageInfo: pageInfo
            });
            return;
        }
        
        console.log('✅ MDAC页面检测成功:', pageInfo);
        sendResponse({
            success: true,
            message: '已检测到MDAC网站页面',
            tabId: tab.id,
            pageInfo: pageInfo
        });
        
    } catch (error) {
        console.error('❌ 页面检测失败:', error);
        sendResponse({
            success: false,
            error: error.message
        });
    }
}

// 处理自动填充执行请求
async function handleExecuteAutoFill(formData, sendResponse) {
    try {
        console.log('🚀 开始执行自动填充...', formData);
        
        const [tab] = await chrome.tabs.query({active: true, currentWindow: true});
        
        if (!tab) {
            throw new Error('无法获取当前标签页');
        }
        
        // 注入并执行填充脚本
        const result = await chrome.scripting.executeScript({
            target: {tabId: tab.id},
            func: fillMDACFormInPage,
            args: [formData]
        });
        
        const fillResult = result[0].result;
        
        console.log('📊 填充结果:', fillResult);
        sendResponse(fillResult);
        
    } catch (error) {
        console.error('❌ 自动填充执行失败:', error);
        sendResponse({
            success: false,
            error: error.message
        });
    }
}

// 处理表单数据保存请求
async function handleSaveFormData(formData, sendResponse) {
    try {
        console.log('💾 保存表单数据...', formData);
        
        const dataToSave = {
            'persistent_email': formData.email || '',
            'persistent_phone': formData.mobile || '',
            'phone_region': formData.region || '60',
            'last_form_data': formData,
            'last_save_time': Date.now()
        };
        
        await chrome.storage.local.set(dataToSave);
        
        console.log('✅ 表单数据保存成功');
        sendResponse({ success: true, message: '数据保存成功' });
        
    } catch (error) {
        console.error('❌ 保存表单数据失败:', error);
        sendResponse({ success: false, error: error.message });
    }
}

// 处理表单数据加载请求
async function handleLoadFormData(sendResponse) {
    try {
        console.log('📂 加载表单数据...');
        
        const keys = ['persistent_email', 'persistent_phone', 'phone_region', 'last_form_data'];
        const result = await chrome.storage.local.get(keys);
        
        console.log('✅ 表单数据加载成功:', result);
        sendResponse({ success: true, data: result });
        
    } catch (error) {
        console.error('❌ 加载表单数据失败:', error);
        sendResponse({ success: false, error: error.message });
    }
}

// 处理扩展信息获取请求
function handleGetExtensionInfo(sendResponse) {
    const manifest = chrome.runtime.getManifest();
    const info = {
        name: manifest.name,
        version: manifest.version,
        description: manifest.description
    };
    
    console.log('ℹ️ 扩展信息:', info);
    sendResponse({ success: true, info: info });
}

// 注入到页面的填充函数（简化版，主要逻辑在mdac-filler.js中）
function fillMDACFormInPage(formData) {
    console.log('🤖 开始在页面中执行表单填充...', formData);
    
    let successCount = 0;
    let totalFields = 0;
    const errors = [];
    
    try {
        // 基础填充函数
        function fillField(selector, value, isSelect = false) {
            totalFields++;
            const element = document.querySelector(selector);
            
            if (element && value !== undefined && value !== null && value !== '') {
                try {
                    if (isSelect && element.tagName === 'SELECT') {
                        element.value = value;
                        const targetOption = element.querySelector(`option[value="${value}"]`);
                        if (targetOption) {
                            targetOption.selected = true;
                        }
                    } else {
                        element.value = value;
                    }
                    
                    // 触发事件
                    element.dispatchEvent(new Event('input', { bubbles: true }));
                    element.dispatchEvent(new Event('change', { bubbles: true }));
                    element.dispatchEvent(new Event('blur', { bubbles: true }));
                    
                    successCount++;
                    console.log(`✅ 填充 ${selector}: ${value}`);
                    return true;
                } catch (error) {
                    console.error(`❌ 填充失败 ${selector}:`, error);
                    errors.push(`填充失败 ${selector}: ${error.message}`);
                    return false;
                }
            } else {
                console.warn(`⚠️ 字段不存在或值为空: ${selector}`);
                return false;
            }
        }
        
        // 执行填充
        console.log('📋 开始填充表单字段...');
        
        // 基本信息
        fillField('#name', formData.name);
        fillField('#passNo', formData.passNo);
        fillField('#dob', formData.dob);
        fillField('#nationality', formData.nationality, true);
        fillField('#sex', formData.sex, true);
        fillField('#passExpDte', formData.passExpDte);
        
        // 联系信息
        fillField('#email', formData.email);
        fillField('#confirmEmail', formData.confirmEmail || formData.email);
        fillField('#region', formData.region, true); // 注意使用region而不是phoneRegion
        fillField('#mobile', formData.mobile);
        
        // 行程信息
        fillField('#arrDt', formData.arrDt);
        fillField('#depDt', formData.depDt);
        fillField('#vesselNm', formData.vesselNm);
        fillField('#trvlMode', formData.trvlMode || '1', true);
        
        // 其他信息
        fillField('#embark', formData.embark || formData.nationality, true);
        
        console.log('✅ 基础表单填充完成');
        
        return {
            success: true,
            message: `表单填充完成！成功填充 ${successCount}/${totalFields} 个字段`,
            successCount,
            totalFields,
            errors: errors.length > 0 ? errors : null
        };
        
    } catch (error) {
        console.error('❌ 填充过程出错:', error);
        return {
            success: false,
            error: error.message,
            successCount,
            totalFields,
            errors
        };
    }
}

// 错误处理
chrome.runtime.onSuspend.addListener(() => {
    console.log('💤 后台服务即将挂起');
});

// 监听标签页更新事件（可选）
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    if (changeInfo.status === 'complete' && tab.url && tab.url.includes('imigresen-online.imi.gov.my/mdac')) {
        console.log('🔄 MDAC页面加载完成:', tab.url);
        // 可以在这里执行一些初始化操作
    }
});

console.log('✅ MDAC智能填充助手后台服务初始化完成');
