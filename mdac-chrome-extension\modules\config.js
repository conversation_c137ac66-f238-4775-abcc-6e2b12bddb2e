// MDAC智能填充助手 - 配置模块
// 包含所有配置常量和硬编码的API密钥

// Gemini API 配置（硬编码，用户不可见）
export const GEMINI_CONFIG = {
    API_KEY: 'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s',
    MODEL: 'gemini-2.5-flash-lite-preview-06-17',
    API_URL: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite-preview-06-17:generateContent'
};

// MDAC网站检测配置
export const MDAC_DETECTION = {
    HOST: 'imigresen-online.imi.gov.my',
    PATH_PATTERN: '/mdac/',
    REQUIRED_FIELDS: ['#name', '#passNo', '#nationality', '#email'],
    PAGE_TITLE_PATTERN: 'Malaysia Digital Arrival Card'
};

// 示例数据
export const SAMPLE_DATA = {
    passengerName: 'LI MING',
    passportNo: 'G12345678',
    birthDate: '01/01/1990',
    nationality: 'CHN',
    gender: '1',
    passportExpiry: '01/01/2030',
    email: '<EMAIL>',
    confirmEmail: '<EMAIL>',
    phoneRegion: '60',
    mobile: '*********',
    arrivalDate: '01/12/2024',
    departureDate: '05/12/2024',
    vesselName: 'MH123',
    travelMode: '1',
    accommodationState: '14', // 吉隆坡
    accommodationCity: '1400', // 吉隆坡市
    accommodationAddress: 'Hotel KL City Center',
    accommodationPostcode: '50000'
};

// 字段映射配置 - 脚本字段ID到实际网站字段ID的映射
export const FIELD_MAPPING = {
    'name': 'passengerName',
    'passNo': 'passportNo', 
    'dob': 'birthDate',
    'nationality': 'nationality',
    'sex': 'gender',
    'passExpDte': 'passportExpiry',
    'email': 'email',
    'confirmEmail': 'confirmEmail',
    'region': 'phoneRegion',  // 注意：实际网站使用region，不是phoneRegion
    'mobile': 'mobile',
    'arrDt': 'arrivalDate',
    'depDt': 'departureDate',
    'vesselNm': 'vesselName',
    'trvlMode': 'travelMode',
    'accommodationState': 'accommodationState',
    'accommodationCity': 'accommodationCity',
    'accommodationAddress': 'accommodationAddress',
    'accommodationPostcode': 'accommodationPostcode'
};

// 实际网站字段ID映射（处理字段ID差异）
export const ACTUAL_FIELD_MAPPING = {
    'phoneRegion': 'region',  // 脚本中使用phoneRegion，实际网站使用region
    'region': 'region'        // 保持一致性
};

// 国籍代码映射
export const NATIONALITY_CODES = {
    '中国': 'CHN',
    '马来西亚': 'MYS', 
    '新加坡': 'SGP',
    '美国': 'USA',
    '英国': 'GBR',
    '澳大利亚': 'AUS',
    '加拿大': 'CAN',
    '日本': 'JPN',
    '韩国': 'KOR',
    '泰国': 'THA',
    '印度尼西亚': 'IDN',
    '菲律宾': 'PHL',
    '越南': 'VNM',
    '印度': 'IND'
};

// 电话区号映射
export const PHONE_REGION_CODES = {
    '中国': '86',
    '马来西亚': '60',
    '新加坡': '65', 
    '美国': '1',
    '英国': '44',
    '澳大利亚': '61',
    '加拿大': '1',
    '日本': '81',
    '韩国': '82',
    '泰国': '66',
    '印度尼西亚': '62',
    '菲律宾': '63',
    '越南': '84',
    '印度': '91'
};

// 性别映射
export const GENDER_MAPPING = {
    '男': '1',
    '女': '2',
    '男性': '1',
    '女性': '2',
    'male': '1',
    'female': '2',
    'M': '1',
    'F': '2'
};

// 交通方式映射
export const TRAVEL_MODE_MAPPING = {
    '飞机': '1',
    '航班': '1',
    '船': '2',
    '轮船': '2',
    '陆路': '3',
    '汽车': '3',
    '巴士': '3'
};

// 马来西亚州属代码
export const MALAYSIA_STATES = {
    '01': 'JOHOR',
    '02': 'KEDAH', 
    '03': 'KELANTAN',
    '04': 'MELAKA',
    '05': 'NEGERI SEMBILAN',
    '06': 'PAHANG',
    '07': 'PERAK',
    '08': 'PERLIS',
    '09': 'PULAU PINANG',
    '10': 'SABAH',
    '11': 'SARAWAK',
    '12': 'SELANGOR',
    '13': 'TERENGGANU',
    '14': 'WILAYAH PERSEKUTUAN KUALA LUMPUR',
    '15': 'WILAYAH PERSEKUTUAN LABUAN',
    '16': 'WILAYAH PERSEKUTUAN PUTRAJAYA'
};

// 错误消息
export const ERROR_MESSAGES = {
    'NOT_MDAC_PAGE': '请先打开MDAC网站页面',
    'FORM_NOT_LOADED': 'MDAC表单未正确加载，请刷新页面',
    'AI_PARSE_FAILED': '信息解析失败，请检查输入内容',
    'FILL_FAILED': '表单填充失败，请手动检查',
    'NETWORK_ERROR': '网络连接失败，请检查网络',
    'API_ERROR': 'AI服务暂时不可用，请稍后重试',
    'VALIDATION_ERROR': '数据验证失败，请检查输入信息',
    'PERMISSION_ERROR': '权限不足，请确保在MDAC网站页面'
};

// 成功消息
export const SUCCESS_MESSAGES = {
    'PAGE_DETECTED': '✅ 已检测到MDAC网站页面',
    'AI_PARSED': '✅ AI解析完成',
    'FORM_FILLED': '✅ 表单填充完成',
    'DATA_SAVED': '✅ 数据已保存'
};

// 状态消息
export const STATUS_MESSAGES = {
    'DETECTING_PAGE': '🔍 检测页面中...',
    'PARSING_AI': '🤖 AI解析中...',
    'FILLING_FORM': '📝 填充表单中...',
    'SAVING_DATA': '💾 保存数据中...',
    'READY': '✨ 准备就绪',
    'WAITING': '⏳ 等待操作...'
};

// 存储键名
export const STORAGE_KEYS = {
    PERSISTENT_EMAIL: 'persistent_email',
    PERSISTENT_PHONE: 'persistent_phone', 
    PHONE_REGION: 'phone_region',
    LAST_FORM_DATA: 'last_form_data',
    USER_PREFERENCES: 'user_preferences'
};

// AI提示模板
export const AI_PROMPT_TEMPLATE = `
你是一个专业的MDAC表单数据提取助手。请从以下旅客信息中提取数据，并返回严格的JSON格式。

要求：
1. 只返回JSON格式，不要任何其他文字
2. 所有字段都是可选的，如果信息不明确或缺失，设为null
3. 日期格式统一为DD/MM/YYYY
4. 姓名必须大写
5. 护照号码必须大写
6. 国籍使用3位国家代码（如CHN、MYS、USA）
7. 性别：男性=1，女性=2
8. 电话区号只填数字（如60、86、1）
9. 交通方式：飞机=1，船=2，陆路=3

JSON格式示例：
{
  "name": "LI MING",
  "passNo": "G12345678", 
  "dob": "01/01/1990",
  "nationality": "CHN",
  "sex": "1",
  "passExpDte": "01/01/2030",
  "email": "<EMAIL>",
  "confirmEmail": "<EMAIL>", 
  "region": "60",
  "mobile": "*********",
  "arrDt": "01/12/2024",
  "depDt": "05/12/2024",
  "vesselNm": "MH123",
  "trvlMode": "1",
  "accommodationAddress": "Hotel Address",
  "accommodationPostcode": "50000"
}

请提取以下信息：
`;

// 导出默认配置对象
export default {
    GEMINI_CONFIG,
    MDAC_DETECTION,
    SAMPLE_DATA,
    FIELD_MAPPING,
    ACTUAL_FIELD_MAPPING,
    NATIONALITY_CODES,
    PHONE_REGION_CODES,
    GENDER_MAPPING,
    TRAVEL_MODE_MAPPING,
    MALAYSIA_STATES,
    ERROR_MESSAGES,
    SUCCESS_MESSAGES,
    STATUS_MESSAGES,
    STORAGE_KEYS,
    AI_PROMPT_TEMPLATE
};
