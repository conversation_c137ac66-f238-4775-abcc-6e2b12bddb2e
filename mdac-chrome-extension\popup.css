/* MDAC智能填充助手 - Chrome扩展样式 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.4;
    color: #333;
    background: #f8f9fa;
    width: 420px;
    min-height: 600px;
    font-size: 13px;
}

.container {
    width: 100%;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 12px 16px;
    text-align: center;
}

.header h1 {
    font-size: 1.3em;
    margin-bottom: 4px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.subtitle {
    font-size: 0.85em;
    opacity: 0.9;
}

/* 页面状态指示器 */
.page-status {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    background: #e3f2fd;
    border-bottom: 1px solid #bbdefb;
}

.status-indicator {
    font-size: 16px;
}

.status-text {
    font-size: 13px;
    color: #1565c0;
    font-weight: 500;
}

.page-status.success {
    background: #e8f5e8;
    border-bottom-color: #c8e6c9;
}

.page-status.success .status-text {
    color: #2e7d32;
}

.page-status.error {
    background: #ffebee;
    border-bottom-color: #ffcdd2;
}

.page-status.error .status-text {
    color: #c62828;
}

/* 主要内容区域 */
.main-content {
    padding: 16px;
}

/* 输入区域 */
.input-section {
    margin-bottom: 20px;
}

.section-title {
    font-size: 1.1em;
    color: #2c3e50;
    margin-bottom: 12px;
    border-bottom: 2px solid #3498db;
    padding-bottom: 4px;
}

.ai-input-area {
    position: relative;
    margin-bottom: 12px;
}

.auto-parse-indicator {
    position: absolute;
    top: -20px;
    right: 0;
    font-size: 11px;
    color: #666;
    background: #fff3cd;
    padding: 2px 6px;
    border-radius: 3px;
    border: 1px solid #ffeaa7;
    display: none;
}

.auto-parse-indicator.active {
    display: block;
}

.ai-input {
    width: 100%;
    min-height: 120px;
    padding: 12px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 13px;
    font-family: inherit;
    resize: vertical;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.ai-input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.ai-input::placeholder {
    color: #6c757d;
    line-height: 1.4;
}

/* 快速操作按钮 */
.quick-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 5px;
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.btn-secondary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
}

.btn:active {
    transform: translateY(0);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn-large {
    width: 100%;
    padding: 12px 20px;
    font-size: 14px;
}

/* 表单区域 */
.form-section {
    margin-bottom: 20px;
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 12px;
}

.section-header:hover {
    background: #f8f9fa;
    margin: 0 -8px 12px -8px;
    padding: 8px;
    border-radius: 4px;
}

.toggle-hint {
    font-size: 0.8em;
    color: #6c757d;
    font-weight: normal;
}

.toggle-icon {
    font-size: 14px;
    color: #6c757d;
    transition: transform 0.2s ease;
}

.toggle-icon.collapsed {
    transform: rotate(-90deg);
}

.form-fields {
    display: grid;
    gap: 12px;
    transition: all 0.3s ease;
}

.form-fields.collapsed {
    display: none;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 4px;
    font-weight: 600;
    color: #555;
    font-size: 12px;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 8px 10px;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    font-size: 13px;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.1);
}

/* 执行区域 */
.action-section {
    border-top: 1px solid #e9ecef;
    padding-top: 16px;
}

.status-display {
    margin-top: 12px;
}

.status-message {
    text-align: center;
    padding: 8px;
    border-radius: 4px;
    font-weight: 500;
    margin-bottom: 8px;
}

.status-message.info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.status-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-message.loading {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

/* 进度条 */
.progress-bar {
    width: 100%;
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 3px;
}

/* 加载动画 */
.spinner {
    display: inline-block;
    width: 14px;
    height: 14px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 6px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式调整 */
@media (max-width: 480px) {
    body {
        width: 100%;
        min-width: 320px;
    }
    
    .quick-actions {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
    }
}
