<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MDAC智能填充助手</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <!-- 头部标题 -->
        <div class="header">
            <h1>🚀 MDAC智能填充助手</h1>
            <p class="subtitle">一键智能填充马来西亚数字入境卡</p>
        </div>

        <!-- 页面检测状态 -->
        <div class="page-status" id="pageStatus">
            <span class="status-indicator" id="statusIndicator">🔍</span>
            <span class="status-text" id="statusText">检测页面中...</span>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- AI智能输入区域 -->
            <div class="input-section">
                <h2 class="section-title">📝 旅客信息输入</h2>
                
                <div class="ai-input-area">
                    <div class="auto-parse-indicator" id="autoParseIndicator">⚡ 1秒后自动解析</div>
                    <textarea 
                        id="travelerInfo" 
                        class="ai-input" 
                        placeholder="⚡ 智能自动解析功能已启用！

只需输入超过10个字符，系统将在1秒后自动使用AI解析并填充表单！

支持格式：
1. 自然语言：
李明，中国护照G12345678，1990年1月1日出生，男性，护照2026年1月1日到期。邮箱：<EMAIL>，手机：+60123456789。计划2025年8月1日到达马来西亚，8月7日离开。乘坐MH123航班，住宿地址：吉隆坡市中心酒店，邮编50000

2. 结构化数据：
name: LI MING
passport: G12345678  
birth: 1990-01-01
nationality: CHN
gender: 男
email: <EMAIL>
phone: +60123456789
arrival: 2025-08-01
departure: 2025-08-07
flight: MH123
address: Hotel KL City Center
postcode: 50000

💡 提示：输入内容后稍等1秒，AI会自动解析！"></textarea>
                </div>
                
                <div class="quick-actions">
                    <button type="button" class="btn btn-secondary" id="fillSampleBtn">📋 填入示例</button>
                    <button type="button" class="btn btn-secondary" id="clearFormBtn">🗑️ 清空</button>
                    <button type="button" class="btn btn-primary" id="aiParseBtn">⚡ 立即解析</button>
                </div>
            </div>

            <!-- 表单字段区域（可折叠） -->
            <div class="form-section">
                <div class="section-header" id="formSectionHeader">
                    <h2 class="section-title">👤 个人信息 <span class="toggle-hint">(点击展开/收起)</span></h2>
                    <span class="toggle-icon" id="formToggleIcon">▼</span>
                </div>
                
                <div class="form-fields" id="formFields">
                    <!-- 基本信息 -->
                    <div class="form-group">
                        <label for="passengerName">姓名 *</label>
                        <input type="text" id="passengerName" placeholder="如：LI MING">
                    </div>
                    
                    <div class="form-group">
                        <label for="passportNo">护照号码 *</label>
                        <input type="text" id="passportNo" placeholder="如：G12345678">
                    </div>
                    
                    <div class="form-group">
                        <label for="birthDate">出生日期 *</label>
                        <input type="text" id="birthDate" placeholder="DD/MM/YYYY">
                    </div>
                    
                    <div class="form-group">
                        <label for="nationality">国籍 *</label>
                        <select id="nationality">
                            <option value="">请选择国籍</option>
                            <option value="CHN">中国 (CHN)</option>
                            <option value="MYS">马来西亚 (MYS)</option>
                            <option value="SGP">新加坡 (SGP)</option>
                            <option value="USA">美国 (USA)</option>
                            <option value="GBR">英国 (GBR)</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="gender">性别 *</label>
                        <select id="gender">
                            <option value="">请选择性别</option>
                            <option value="1">男性</option>
                            <option value="2">女性</option>
                        </select>
                    </div>
                    
                    <!-- 联系信息 -->
                    <div class="form-group">
                        <label for="email">邮箱地址 *</label>
                        <input type="email" id="email" placeholder="<EMAIL>">
                    </div>
                    
                    <div class="form-group">
                        <label for="phoneRegion">电话区号 *</label>
                        <select id="phoneRegion">
                            <option value="">请选择区号</option>
                            <option value="60">+60 (马来西亚)</option>
                            <option value="86">+86 (中国)</option>
                            <option value="65">+65 (新加坡)</option>
                            <option value="1">+1 (美国)</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="mobile">手机号码 *</label>
                        <input type="text" id="mobile" placeholder="123456789">
                    </div>
                    
                    <!-- 行程信息 -->
                    <div class="form-group">
                        <label for="arrivalDate">到达日期 *</label>
                        <input type="text" id="arrivalDate" placeholder="DD/MM/YYYY">
                    </div>
                    
                    <div class="form-group">
                        <label for="departureDate">离开日期 *</label>
                        <input type="text" id="departureDate" placeholder="DD/MM/YYYY">
                    </div>
                    
                    <div class="form-group">
                        <label for="vesselName">航班/船只号 *</label>
                        <input type="text" id="vesselName" placeholder="如：MH123">
                    </div>
                </div>
            </div>
        </div>

        <!-- 执行按钮和状态 -->
        <div class="action-section">
            <button type="button" class="btn btn-primary btn-large" id="executeBtn" disabled>
                🎯 开始智能填充
            </button>
            
            <div class="status-display" id="statusDisplay">
                <div class="status-message" id="statusMessage">等待填充...</div>
                <div class="progress-bar" id="progressBar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载脚本 -->
    <script src="popup.js" type="module"></script>
</body>
</html>
