{"permissions": {"allow": ["<PERSON><PERSON>(claude config list)", "Bash(claude mcp add:*)", "<PERSON><PERSON>(claude mcp:*)", "mcp__streamable-mcp-server__chrome_get_web_content", "mcp__streamable-mcp-server__chrome_screenshot", "mcp__streamable-mcp-server__chrome_get_interactive_elements", "mcp__streamable-mcp-server__chrome_console", "mcp__streamable-mcp-server__get_windows_and_tabs", "mcp__streamable-mcp-server__chrome_navigate", "mcp__streamable-mcp-server__chrome_inject_script", "mcp__streamable-mcp-server__chrome_fill_or_select", "mcp__streamable-mcp-server__chrome_click_element", "Bash(node:*)", "Bash(ls:*)", "<PERSON><PERSON>(timeout 3 sleep 3)"], "deny": []}}