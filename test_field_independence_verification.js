// 字段独立性验证测试脚本
// 验证国籍和电话区号字段完全独立，无自动同步机制

console.log('🔒 字段独立性验证测试');

// 测试用例：不同国籍和区号的组合
const testCases = [
    {
        name: '中国国籍 + 马来西亚区号',
        nationality: 'CHN',
        region: '60',
        description: '中国用户使用马来西亚电话号码'
    },
    {
        name: '马来西亚国籍 + 中国区号',
        nationality: 'MYS',
        region: '86',
        description: '马来西亚用户使用中国电话号码'
    },
    {
        name: '新加坡国籍 + 美国区号',
        nationality: 'SGP',
        region: '1',
        description: '新加坡用户使用美国电话号码'
    },
    {
        name: '美国国籍 + 英国区号',
        nationality: 'USA',
        region: '44',
        description: '美国用户使用英国电话号码'
    }
];

// 验证字段独立性的核心函数
function verifyFieldIndependence(testCase) {
    console.log(`\n🧪 测试案例: ${testCase.name}`);
    console.log(`📝 描述: ${testCase.description}`);
    
    const nationalityField = document.getElementById('nationality');
    const regionField = document.getElementById('region');
    
    if (!nationalityField || !regionField) {
        console.error('❌ 字段不存在，无法进行测试');
        return false;
    }
    
    // 记录初始值
    const initialNationality = nationalityField.value;
    const initialRegion = regionField.value;
    console.log(`📊 初始值 - 国籍: ${initialNationality}, 区号: ${initialRegion}`);
    
    // 设置测试值
    nationalityField.value = testCase.nationality;
    regionField.value = testCase.region;
    
    // 触发change事件
    nationalityField.dispatchEvent(new Event('change'));
    regionField.dispatchEvent(new Event('change'));
    
    // 等待一段时间，检查是否有自动同步
    setTimeout(() => {
        const finalNationality = nationalityField.value;
        const finalRegion = regionField.value;
        
        console.log(`📊 最终值 - 国籍: ${finalNationality}, 区号: ${finalRegion}`);
        
        // 验证字段值是否保持独立
        const nationalityIndependent = finalNationality === testCase.nationality;
        const regionIndependent = finalRegion === testCase.region;
        
        if (nationalityIndependent && regionIndependent) {
            console.log('✅ 字段独立性验证成功');
            console.log(`✅ 国籍字段保持设置值: ${finalNationality}`);
            console.log(`✅ 区号字段保持设置值: ${finalRegion}`);
            return true;
        } else {
            console.log('❌ 字段独立性验证失败');
            if (!nationalityIndependent) {
                console.log(`❌ 国籍字段被意外更改: 期望 ${testCase.nationality}, 实际 ${finalNationality}`);
            }
            if (!regionIndependent) {
                console.log(`❌ 区号字段被意外更改: 期望 ${testCase.region}, 实际 ${finalRegion}`);
            }
            return false;
        }
    }, 500);
}

// 检查是否存在自动同步监听器
function checkForAutoSyncListeners() {
    console.log('\n🔍 检查自动同步监听器:');
    
    const nationalityField = document.getElementById('nationality');
    if (!nationalityField) {
        console.log('❌ 国籍字段不存在');
        return;
    }
    
    // 检查是否有change事件监听器
    const listeners = getEventListeners ? getEventListeners(nationalityField) : null;
    
    if (listeners && listeners.change) {
        console.log(`⚠️ 发现 ${listeners.change.length} 个change事件监听器`);
        listeners.change.forEach((listener, index) => {
            console.log(`📋 监听器 ${index + 1}:`, listener.listener.toString().substring(0, 100) + '...');
        });
    } else {
        console.log('✅ 未检测到change事件监听器 (或无法访问)');
    }
    
    // 检查HTML中的onchange属性
    const onchangeAttr = nationalityField.getAttribute('onchange');
    if (onchangeAttr) {
        console.log('⚠️ 发现HTML onchange属性:', onchangeAttr);
    } else {
        console.log('✅ 未发现HTML onchange属性');
    }
}

// 运行所有独立性测试
async function runIndependenceTests() {
    console.log('🚀 开始字段独立性验证测试\n');
    
    checkForAutoSyncListeners();
    
    let passedTests = 0;
    const totalTests = testCases.length;
    
    for (let i = 0; i < testCases.length; i++) {
        const testCase = testCases[i];
        const result = verifyFieldIndependence(testCase);
        
        if (result) {
            passedTests++;
        }
        
        // 等待一段时间再进行下一个测试
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // 输出测试结果总结
    setTimeout(() => {
        console.log('\n📊 测试结果总结:');
        console.log(`✅ 通过测试: ${passedTests}/${totalTests}`);
        console.log(`❌ 失败测试: ${totalTests - passedTests}/${totalTests}`);
        
        if (passedTests === totalTests) {
            console.log('🎉 所有测试通过！字段独立性验证成功！');
            console.log('🔒 确认：国籍和电话区号字段完全独立，无自动同步机制');
        } else {
            console.log('⚠️ 部分测试失败，可能存在自动同步机制');
        }
    }, 2000);
}

// 页面加载完成后运行测试
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runIndependenceTests);
} else {
    runIndependenceTests();
}

// 导出测试函数供外部调用
window.fieldIndependenceTest = {
    runTests: runIndependenceTests,
    verifyCase: verifyFieldIndependence,
    checkListeners: checkForAutoSyncListeners
};
