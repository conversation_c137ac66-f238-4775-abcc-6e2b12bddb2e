// 测试数据解析功能
// 模拟你输入的数据
const testInput = `1    陈泰宇    CHEN TAIYU    男    2014年2月7日    2029年7月17日    EM8990705    2024年7月18日    2`;

console.log('🧪 测试数据解析...');
console.log('📝 输入数据:', testInput);

// 模拟解析后的期望结果
const expectedData = {
    name: "CHEN TAIYU",
    passNo: "EM8990705",
    dob: "07/02/2014",
    nationality: "CHN",  // 根据中文姓名推断
    sex: "1",  // 男 = 1
    passExpDte: "17/07/2029",
    email: null,
    confirmEmail: null,
    region: "86",  // 中国区号
    mobile: null,
    arrDt: "18/07/2024",  // 可能是到达日期
    depDt: null,
    vesselNm: null,
    trvlMode: null,
    embark: null,
    accommodationStay: null,
    accommodationAddress1: null,
    accommodationAddress2: null,
    accommodationState: null,
    accommodationPostcode: null
};

console.log('🎯 期望解析结果:', expectedData);

// 测试选择性填充函数
function testSelectiveFilling() {
    console.log('\n🔍 测试选择性填充逻辑...');
    
    const fieldMapping = {
        'name': 'passengerName',
        'passNo': 'passportNo',
        'dob': 'birthDate',
        'nationality': 'nationality',
        'sex': 'gender',
        'passExpDte': 'passportExpiry',
        'email': 'email',
        'confirmEmail': 'confirmEmail',
        'region': 'phoneRegion',
        'mobile': 'phoneNumber',
        'arrDt': 'arrivalDate',
        'depDt': 'departureDate',
        'vesselNm': 'flightNo',
        'trvlMode': 'travelMode',
        'embark': 'embark',
        'accommodationStay': 'accommodationType',
        'accommodationAddress1': 'address1',
        'accommodationAddress2': 'address2',
        'accommodationState': 'state',
        'accommodationPostcode': 'postcode'
    };
    
    let updateCount = 0;
    let skipCount = 0;
    
    Object.keys(fieldMapping).forEach(dataKey => {
        const formFieldId = fieldMapping[dataKey];
        const value = expectedData[dataKey];
        
        if (value === null || value === undefined || value === '') {
            console.log(`⏸️ 跳过空字段 ${dataKey} → ${formFieldId}`);
            skipCount++;
        } else {
            console.log(`✅ 将更新字段 ${dataKey} → ${formFieldId}: "${value}"`);
            updateCount++;
        }
    });
    
    console.log(`📊 预期结果: ${updateCount} 个字段将被更新, ${skipCount} 个字段将被跳过`);
}

testSelectiveFilling();

// 验证数据格式
console.log('\n🔍 验证数据格式...');
console.log('姓名格式:', expectedData.name ? '✅ 有效' : '❌ 缺失');
console.log('护照号格式:', expectedData.passNo ? '✅ 有效' : '❌ 缺失');
console.log('出生日期格式:', expectedData.dob ? '✅ 有效' : '❌ 缺失');
console.log('性别格式:', expectedData.sex ? '✅ 有效' : '❌ 缺失');
console.log('护照到期日格式:', expectedData.passExpDte ? '✅ 有效' : '❌ 缺失');

console.log('\n💡 建议：');
console.log('1. 确保Gemini API能够正确解析这种格式的数据');
console.log('2. 检查是否需要补充邮箱、手机等联系信息');
console.log('3. 验证州属和城市信息是否需要手动填写');