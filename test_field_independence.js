// 测试修复后的字段独立性
console.log('🧪 测试字段独立性...');

// 测试场景1：只提供基本信息
const testCase1 = {
    input: "张三 ZHANG SAN 男 1990年1月1日 2030年12月31日 G1234567",
    expectedFields: ['name', 'sex', 'dob', 'passExpDte', 'passNo', 'nationality'],
    shouldNotChange: ['region', 'embark', 'mobile', 'email', 'arrDt', 'depDt', 'vesselNm', 'accommodationState']
};

// 测试场景2：只提供护照信息
const testCase2 = {
    input: "护照号：E9876543，国籍：美国，到期日：2025年6月15日",
    expectedFields: ['passNo', 'nationality', 'passExpDte'],
    shouldNotChange: ['name', 'region', 'embark', 'mobile', 'email', 'sex', 'dob']
};

// 测试场景3：只提供联系信息
const testCase3 = {
    input: "邮箱：<EMAIL>，手机：+60123456789",
    expectedFields: ['email', 'region', 'mobile'],
    shouldNotChange: ['name', 'passNo', 'nationality', 'embark', 'sex', 'dob', 'passExpDte']
};

function testFieldIndependence(testCase, caseNumber) {
    console.log(`\n🔍 测试案例 ${caseNumber}: ${testCase.input}`);
    
    // 模拟AI解析结果（用户只提供部分信息）
    const mockAIResult = {};
    
    // 根据测试案例设置有值的字段
    testCase.expectedFields.forEach(field => {
        switch(field) {
            case 'name': mockAIResult.name = 'ZHANG SAN'; break;
            case 'sex': mockAIResult.sex = '1'; break;
            case 'dob': mockAIResult.dob = '01/01/1990'; break;
            case 'passExpDte': mockAIResult.passExpDte = '31/12/2030'; break;
            case 'passNo': mockAIResult.passNo = 'G1234567'; break;
            case 'nationality': mockAIResult.nationality = 'CHN'; break;
            case 'email': mockAIResult.email = '<EMAIL>'; break;
            case 'region': mockAIResult.region = '60'; break;
            case 'mobile': mockAIResult.mobile = '123456789'; break;
        }
    });
    
    // 其他字段设置为null
    testCase.shouldNotChange.forEach(field => {
        mockAIResult[field] = null;
    });
    
    console.log('📊 AI解析结果:', mockAIResult);
    
    // 测试修复后的验证逻辑
    const cleaned = testValidateAndCleanData(mockAIResult);
    
    // 验证结果
    let testPassed = true;
    
    // 检查应该有值的字段
    testCase.expectedFields.forEach(field => {
        if (cleaned[field] === null || cleaned[field] === undefined) {
            console.log(`❌ 失败: 字段 ${field} 应该有值但为空`);
            testPassed = false;
        } else {
            console.log(`✅ 通过: 字段 ${field} 正确设置为 "${cleaned[field]}"`);
        }
    });
    
    // 检查不应该被更改的字段
    testCase.shouldNotChange.forEach(field => {
        if (cleaned[field] !== null && cleaned[field] !== undefined) {
            console.log(`❌ 失败: 字段 ${field} 不应该被自动设置，但被设置为 "${cleaned[field]}"`);
            testPassed = false;
        } else {
            console.log(`✅ 通过: 字段 ${field} 正确保持为空`);
        }
    });
    
    console.log(`📋 测试案例 ${caseNumber} 结果: ${testPassed ? '✅ 通过' : '❌ 失败'}`);
    return testPassed;
}

// 修复后的验证逻辑（无自动填充）
function testValidateAndCleanData(data) {
    const cleaned = { ...data };
    
    console.log('🧹 开始数据验证和清理...');
    
    // 只有明确提供email时才自动填充confirmEmail
    if (cleaned.email && cleaned.email !== null && !cleaned.confirmEmail) {
        cleaned.confirmEmail = cleaned.email;
        console.log('🔧 自动填充confirmEmail:', cleaned.email);
    }
    
    // 🚫 不再自动填充region和embark
    console.log('🔍 保持字段独立性 - 跳过自动填充');
    console.log('💡 只有用户明确提供的数据才会被更新');
    
    console.log('🎯 最终清理后的数据:', cleaned);
    return cleaned;
}

// 执行测试
console.log('🚀 开始字段独立性测试...');

const results = [
    testFieldIndependence(testCase1, 1),
    testFieldIndependence(testCase2, 2),
    testFieldIndependence(testCase3, 3)
];

const passedTests = results.filter(result => result).length;
const totalTests = results.length;

console.log(`\n📊 测试总结: ${passedTests}/${totalTests} 个测试通过`);

if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！字段独立性修复成功！');
    console.log('💡 现在各字段都是独立的，只有用户明确提供的信息才会被更新');
} else {
    console.log('⚠️ 部分测试失败，需要进一步调试');
}

console.log('\n📋 修复总结:');
console.log('✅ 1. 禁用了自动填充embark逻辑');
console.log('✅ 2. 禁用了自动填充region逻辑');
console.log('✅ 3. 增强了prompt中的独立性指令');
console.log('✅ 4. 保持了confirmEmail的合理自动填充');
console.log('✅ 5. 确保只有用户明确提供的字段才会被更新');