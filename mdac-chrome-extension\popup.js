// MDAC智能填充助手 - Popup主控制脚本
// 整合所有功能模块，处理用户界面交互

import { extractDataWithGemini, setupAutoAnalysis } from './modules/ai-processor.js';
import { 
    collectFormData, 
    fillFormFields, 
    fillFormFieldsSelectively, 
    clearForm, 
    fillSampleData,
    validateFormData,
    loadPersistentData,
    applyPersistentData
} from './modules/form-handler.js';
import { SAMPLE_DATA, ERROR_MESSAGES, SUCCESS_MESSAGES, STATUS_MESSAGES } from './modules/config.js';

// 全局状态
let isPageDetected = false;
let currentTabId = null;
let isFormCollapsed = true;

// DOM元素引用
let elements = {};

// 初始化扩展
document.addEventListener('DOMContentLoaded', async () => {
    console.log('🚀 MDAC智能填充助手初始化...');
    
    try {
        // 获取DOM元素引用
        initializeElements();
        
        // 设置事件监听器
        setupEventListeners();
        
        // 检测当前页面
        await detectCurrentPage();
        
        // 加载持久化数据
        await loadAndApplyPersistentData();
        
        // 设置AI自动解析
        setupAIAutoAnalysis();
        
        console.log('✅ 扩展初始化完成');
        
    } catch (error) {
        console.error('❌ 扩展初始化失败:', error);
        updateStatus('初始化失败: ' + error.message, 'error');
    }
});

// 初始化DOM元素引用
function initializeElements() {
    elements = {
        // 状态显示
        pageStatus: document.getElementById('pageStatus'),
        statusIndicator: document.getElementById('statusIndicator'),
        statusText: document.getElementById('statusText'),
        
        // 输入区域
        travelerInfo: document.getElementById('travelerInfo'),
        autoParseIndicator: document.getElementById('autoParseIndicator'),
        
        // 按钮
        fillSampleBtn: document.getElementById('fillSampleBtn'),
        clearFormBtn: document.getElementById('clearFormBtn'),
        aiParseBtn: document.getElementById('aiParseBtn'),
        executeBtn: document.getElementById('executeBtn'),
        
        // 表单区域
        formSectionHeader: document.getElementById('formSectionHeader'),
        formToggleIcon: document.getElementById('formToggleIcon'),
        formFields: document.getElementById('formFields'),
        
        // 状态显示
        statusMessage: document.getElementById('statusMessage'),
        progressBar: document.getElementById('progressBar'),
        progressFill: document.getElementById('progressFill')
    };
    
    console.log('📋 DOM元素引用初始化完成');
}

// 设置事件监听器
function setupEventListeners() {
    // 快速操作按钮
    elements.fillSampleBtn?.addEventListener('click', handleFillSample);
    elements.clearFormBtn?.addEventListener('click', handleClearForm);
    elements.aiParseBtn?.addEventListener('click', handleAIParse);
    
    // 主要执行按钮
    elements.executeBtn?.addEventListener('click', handleExecuteAutoFill);
    
    // 表单折叠/展开
    elements.formSectionHeader?.addEventListener('click', handleFormToggle);
    
    console.log('🔗 事件监听器设置完成');
}

// 检测当前页面
async function detectCurrentPage() {
    try {
        updatePageStatus('detecting', '检测页面中...');
        
        const response = await sendMessageToBackground({
            type: 'DETECT_MDAC_PAGE'
        });
        
        if (response.success) {
            isPageDetected = true;
            currentTabId = response.tabId;
            updatePageStatus('success', '✅ 已检测到MDAC网站页面');
            elements.executeBtn.disabled = false;
            
            console.log('✅ 页面检测成功:', response.pageInfo);
        } else {
            isPageDetected = false;
            updatePageStatus('error', response.error || '页面检测失败');
            elements.executeBtn.disabled = true;
            
            console.log('❌ 页面检测失败:', response.error);
        }
        
    } catch (error) {
        console.error('❌ 页面检测异常:', error);
        updatePageStatus('error', '页面检测异常: ' + error.message);
        elements.executeBtn.disabled = true;
    }
}

// 加载并应用持久化数据
async function loadAndApplyPersistentData() {
    try {
        console.log('📂 加载持久化数据...');
        
        const response = await sendMessageToBackground({
            type: 'LOAD_FORM_DATA'
        });
        
        if (response.success && response.data) {
            applyPersistentData(response.data);
            console.log('✅ 持久化数据应用成功');
        }
        
    } catch (error) {
        console.error('❌ 加载持久化数据失败:', error);
    }
}

// 设置AI自动解析
function setupAIAutoAnalysis() {
    if (elements.travelerInfo) {
        setupAutoAnalysis(elements.travelerInfo, async (text) => {
            try {
                await handleAIParse();
            } catch (error) {
                console.error('❌ 自动解析失败:', error);
            }
        });
        console.log('🤖 AI自动解析设置完成');
    }
}

// 处理填入示例数据
function handleFillSample() {
    try {
        console.log('📋 填入示例数据...');
        fillSampleData();
        updateStatus('示例数据已填入', 'success');
    } catch (error) {
        console.error('❌ 填入示例数据失败:', error);
        updateStatus('填入示例数据失败: ' + error.message, 'error');
    }
}

// 处理清空表单
function handleClearForm() {
    try {
        console.log('🗑️ 清空表单...');
        clearForm();
        updateStatus('表单已清空', 'info');
    } catch (error) {
        console.error('❌ 清空表单失败:', error);
        updateStatus('清空表单失败: ' + error.message, 'error');
    }
}

// 处理AI解析
async function handleAIParse() {
    const travelerText = elements.travelerInfo?.value?.trim();
    
    if (!travelerText || travelerText.length < 10) {
        updateStatus('请输入至少10个字符的旅客信息', 'error');
        return;
    }
    
    try {
        console.log('🤖 开始AI解析...');
        updateStatus('AI解析中...', 'loading');
        elements.aiParseBtn.disabled = true;
        
        // 调用AI解析
        const parsedData = await extractDataWithGemini(travelerText);
        
        // 选择性填充表单
        const filledCount = fillFormFieldsSelectively(parsedData);
        
        updateStatus(`AI解析完成！已填充 ${filledCount} 个字段`, 'success');
        console.log('✅ AI解析完成:', parsedData);
        
    } catch (error) {
        console.error('❌ AI解析失败:', error);
        updateStatus('AI解析失败: ' + error.message, 'error');
    } finally {
        elements.aiParseBtn.disabled = false;
    }
}

// 处理表单折叠/展开
function handleFormToggle() {
    isFormCollapsed = !isFormCollapsed;
    
    if (isFormCollapsed) {
        elements.formFields.classList.add('collapsed');
        elements.formToggleIcon.classList.add('collapsed');
        elements.formToggleIcon.textContent = '▶';
    } else {
        elements.formFields.classList.remove('collapsed');
        elements.formToggleIcon.classList.remove('collapsed');
        elements.formToggleIcon.textContent = '▼';
    }
    
    console.log('🔄 表单折叠状态:', isFormCollapsed ? '收起' : '展开');
}

// 处理执行自动填充
async function handleExecuteAutoFill() {
    if (!isPageDetected) {
        updateStatus('请先打开MDAC网站页面', 'error');
        return;
    }
    
    try {
        console.log('🚀 开始执行自动填充...');
        updateStatus('准备填充数据...', 'loading');
        updateProgress(10);
        
        elements.executeBtn.disabled = true;
        
        // 收集表单数据
        const formData = collectFormData();
        console.log('📋 收集的表单数据:', formData);
        
        // 验证表单数据
        const validation = validateFormData(formData);
        if (!validation.isValid) {
            throw new Error('数据验证失败: ' + validation.errors.join(', '));
        }
        
        updateStatus('验证数据完成，开始填充...', 'loading');
        updateProgress(30);
        
        // 保存持久化数据
        await sendMessageToBackground({
            type: 'SAVE_FORM_DATA',
            data: formData
        });
        
        updateStatus('正在填充表单...', 'loading');
        updateProgress(50);
        
        // 执行自动填充
        const response = await sendMessageToBackground({
            type: 'EXECUTE_AUTO_FILL',
            data: validation.validatedData
        });
        
        updateProgress(90);
        
        if (response.success) {
            updateStatus(response.message || '表单填充完成！', 'success');
            updateProgress(100);
            
            // 显示详细结果
            if (response.successCount && response.totalFields) {
                setTimeout(() => {
                    updateStatus(`成功填充 ${response.successCount}/${response.totalFields} 个字段`, 'success');
                }, 2000);
            }
            
            console.log('✅ 自动填充完成:', response);
        } else {
            throw new Error(response.error || '填充失败');
        }
        
    } catch (error) {
        console.error('❌ 自动填充失败:', error);
        updateStatus('填充失败: ' + error.message, 'error');
        updateProgress(0);
    } finally {
        elements.executeBtn.disabled = false;
        
        // 3秒后重置进度条
        setTimeout(() => {
            updateProgress(0);
        }, 3000);
    }
}

// 更新页面状态显示
function updatePageStatus(type, message) {
    if (!elements.pageStatus) return;
    
    // 移除所有状态类
    elements.pageStatus.classList.remove('success', 'error');
    
    // 添加新状态类
    if (type !== 'detecting') {
        elements.pageStatus.classList.add(type);
    }
    
    // 更新指示器和文本
    if (elements.statusIndicator) {
        const indicators = {
            'detecting': '🔍',
            'success': '✅',
            'error': '❌'
        };
        elements.statusIndicator.textContent = indicators[type] || '🔍';
    }
    
    if (elements.statusText) {
        elements.statusText.textContent = message;
    }
}

// 更新状态消息
function updateStatus(message, type = 'info') {
    if (!elements.statusMessage) return;
    
    // 移除所有状态类
    elements.statusMessage.classList.remove('info', 'success', 'error', 'loading');
    
    // 添加新状态类
    elements.statusMessage.classList.add(type);
    
    // 更新消息内容
    if (type === 'loading') {
        elements.statusMessage.innerHTML = `<span class="spinner"></span> ${message}`;
    } else {
        elements.statusMessage.textContent = message;
    }
    
    console.log(`📢 状态更新 [${type}]: ${message}`);
}

// 更新进度条
function updateProgress(percentage) {
    if (!elements.progressFill) return;
    
    elements.progressFill.style.width = `${percentage}%`;
    
    if (percentage > 0) {
        elements.progressBar.style.opacity = '1';
    } else {
        elements.progressBar.style.opacity = '0.3';
    }
}

// 发送消息到后台脚本
function sendMessageToBackground(message) {
    return new Promise((resolve, reject) => {
        chrome.runtime.sendMessage(message, (response) => {
            if (chrome.runtime.lastError) {
                reject(new Error(chrome.runtime.lastError.message));
            } else {
                resolve(response);
            }
        });
    });
}

// 错误处理
window.addEventListener('error', (event) => {
    console.error('❌ 全局错误:', event.error);
    updateStatus('发生错误: ' + event.error.message, 'error');
});

window.addEventListener('unhandledrejection', (event) => {
    console.error('❌ 未处理的Promise拒绝:', event.reason);
    updateStatus('异步操作失败: ' + event.reason, 'error');
});

console.log('📜 Popup脚本加载完成');
