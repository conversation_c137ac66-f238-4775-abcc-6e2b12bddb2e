// MDAC智能填充助手 - 数据验证模块
// 负责各种数据格式验证和转换

import { 
    NATIONALITY_CODES, 
    PHONE_REGION_CODES, 
    GENDER_MAPPING, 
    TRAVEL_MODE_MAPPING,
    MALAYSIA_STATES,
    ERROR_MESSAGES 
} from './config.js';

/**
 * 验证和标准化姓名
 * @param {string} name - 姓名
 * @returns {Object} {isValid: boolean, value: string, error?: string}
 */
export function validateName(name) {
    if (!name || typeof name !== 'string') {
        return { isValid: false, value: '', error: '姓名不能为空' };
    }
    
    const cleanName = name.trim().toUpperCase();
    
    if (cleanName.length < 2) {
        return { isValid: false, value: cleanName, error: '姓名至少需要2个字符' };
    }
    
    if (cleanName.length > 60) {
        return { isValid: false, value: cleanName, error: '姓名不能超过60个字符' };
    }
    
    // 检查是否只包含字母和空格
    const nameRegex = /^[A-Z\s]+$/;
    if (!nameRegex.test(cleanName)) {
        return { isValid: false, value: cleanName, error: '姓名只能包含英文字母和空格' };
    }
    
    return { isValid: true, value: cleanName };
}

/**
 * 验证护照号码
 * @param {string} passportNo - 护照号码
 * @returns {Object} {isValid: boolean, value: string, error?: string}
 */
export function validatePassportNo(passportNo) {
    if (!passportNo || typeof passportNo !== 'string') {
        return { isValid: false, value: '', error: '护照号码不能为空' };
    }
    
    const cleanPassport = passportNo.trim().toUpperCase();
    
    if (cleanPassport.length < 6 || cleanPassport.length > 12) {
        return { isValid: false, value: cleanPassport, error: '护照号码长度应在6-12位之间' };
    }
    
    // 检查是否只包含字母和数字
    const passportRegex = /^[A-Z0-9]+$/;
    if (!passportRegex.test(cleanPassport)) {
        return { isValid: false, value: cleanPassport, error: '护照号码只能包含英文字母和数字' };
    }
    
    return { isValid: true, value: cleanPassport };
}

/**
 * 验证和转换日期格式
 * @param {string} dateStr - 日期字符串
 * @param {string} fieldName - 字段名称
 * @returns {Object} {isValid: boolean, value: string, error?: string}
 */
export function validateDate(dateStr, fieldName = '日期') {
    if (!dateStr || typeof dateStr !== 'string') {
        return { isValid: false, value: '', error: `${fieldName}不能为空` };
    }
    
    try {
        let date;
        const cleanDate = dateStr.trim();
        
        // 尝试解析不同的日期格式
        if (cleanDate.includes('/')) {
            const parts = cleanDate.split('/');
            if (parts.length === 3) {
                if (parts[0].length === 4) {
                    // YYYY/MM/DD
                    date = new Date(parseInt(parts[0]), parseInt(parts[1]) - 1, parseInt(parts[2]));
                } else if (parts[2].length === 4) {
                    // DD/MM/YYYY
                    date = new Date(parseInt(parts[2]), parseInt(parts[1]) - 1, parseInt(parts[0]));
                }
            }
        } else if (cleanDate.includes('-')) {
            const parts = cleanDate.split('-');
            if (parts.length === 3) {
                if (parts[0].length === 4) {
                    // YYYY-MM-DD
                    date = new Date(parseInt(parts[0]), parseInt(parts[1]) - 1, parseInt(parts[2]));
                } else {
                    // DD-MM-YYYY
                    date = new Date(parseInt(parts[2]), parseInt(parts[1]) - 1, parseInt(parts[0]));
                }
            }
        } else {
            // 尝试直接解析
            date = new Date(cleanDate);
        }
        
        if (!date || isNaN(date.getTime())) {
            return { isValid: false, value: cleanDate, error: `${fieldName}格式不正确` };
        }
        
        // 检查日期是否合理
        const currentYear = new Date().getFullYear();
        const year = date.getFullYear();
        
        if (year < 1900 || year > currentYear + 50) {
            return { isValid: false, value: cleanDate, error: `${fieldName}年份不合理` };
        }
        
        // 格式化为DD/MM/YYYY
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const formattedDate = `${day}/${month}/${year}`;
        
        return { isValid: true, value: formattedDate };
    } catch (error) {
        return { isValid: false, value: dateStr, error: `${fieldName}格式错误: ${error.message}` };
    }
}

/**
 * 验证国籍代码
 * @param {string} nationality - 国籍
 * @returns {Object} {isValid: boolean, value: string, error?: string}
 */
export function validateNationality(nationality) {
    if (!nationality || typeof nationality !== 'string') {
        return { isValid: false, value: '', error: '国籍不能为空' };
    }
    
    const cleanNationality = nationality.trim().toUpperCase();
    
    // 如果是中文国籍名，转换为代码
    if (NATIONALITY_CODES[nationality]) {
        return { isValid: true, value: NATIONALITY_CODES[nationality] };
    }
    
    // 检查是否是有效的3位国家代码
    if (cleanNationality.length === 3 && /^[A-Z]{3}$/.test(cleanNationality)) {
        return { isValid: true, value: cleanNationality };
    }
    
    return { isValid: false, value: cleanNationality, error: '国籍代码格式不正确，应为3位字母代码' };
}

/**
 * 验证性别
 * @param {string|number} gender - 性别
 * @returns {Object} {isValid: boolean, value: string, error?: string}
 */
export function validateGender(gender) {
    if (!gender) {
        return { isValid: false, value: '', error: '性别不能为空' };
    }
    
    const genderStr = String(gender).trim();
    
    // 直接是数字代码
    if (['1', '2'].includes(genderStr)) {
        return { isValid: true, value: genderStr };
    }
    
    // 中文或英文性别，转换为代码
    if (GENDER_MAPPING[genderStr]) {
        return { isValid: true, value: GENDER_MAPPING[genderStr] };
    }
    
    return { isValid: false, value: genderStr, error: '性别格式不正确' };
}

/**
 * 验证邮箱地址
 * @param {string} email - 邮箱地址
 * @returns {Object} {isValid: boolean, value: string, error?: string}
 */
export function validateEmail(email) {
    if (!email || typeof email !== 'string') {
        return { isValid: false, value: '', error: '邮箱地址不能为空' };
    }
    
    const cleanEmail = email.trim().toLowerCase();
    
    if (cleanEmail.length > 100) {
        return { isValid: false, value: cleanEmail, error: '邮箱地址不能超过100个字符' };
    }
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(cleanEmail)) {
        return { isValid: false, value: cleanEmail, error: '邮箱地址格式不正确' };
    }
    
    return { isValid: true, value: cleanEmail };
}

/**
 * 验证电话区号
 * @param {string|number} region - 电话区号
 * @returns {Object} {isValid: boolean, value: string, error?: string}
 */
export function validatePhoneRegion(region) {
    if (!region) {
        return { isValid: false, value: '', error: '电话区号不能为空' };
    }
    
    const regionStr = String(region).trim().replace(/^\+/, '');
    
    // 检查是否是纯数字
    if (!/^\d+$/.test(regionStr)) {
        return { isValid: false, value: regionStr, error: '电话区号只能包含数字' };
    }
    
    // 检查长度
    if (regionStr.length < 1 || regionStr.length > 4) {
        return { isValid: false, value: regionStr, error: '电话区号长度不正确' };
    }
    
    return { isValid: true, value: regionStr };
}

/**
 * 验证手机号码
 * @param {string} mobile - 手机号码
 * @returns {Object} {isValid: boolean, value: string, error?: string}
 */
export function validateMobile(mobile) {
    if (!mobile || typeof mobile !== 'string') {
        return { isValid: false, value: '', error: '手机号码不能为空' };
    }
    
    // 清理手机号码，只保留数字
    const cleanMobile = mobile.replace(/[^\d]/g, '');
    
    if (cleanMobile.length < 7 || cleanMobile.length > 15) {
        return { isValid: false, value: cleanMobile, error: '手机号码长度不正确' };
    }
    
    return { isValid: true, value: cleanMobile };
}

/**
 * 验证交通方式
 * @param {string|number} travelMode - 交通方式
 * @returns {Object} {isValid: boolean, value: string, error?: string}
 */
export function validateTravelMode(travelMode) {
    if (!travelMode) {
        return { isValid: true, value: '1' }; // 默认飞机
    }
    
    const modeStr = String(travelMode).trim();
    
    // 直接是数字代码
    if (['1', '2', '3'].includes(modeStr)) {
        return { isValid: true, value: modeStr };
    }
    
    // 中文交通方式，转换为代码
    if (TRAVEL_MODE_MAPPING[modeStr]) {
        return { isValid: true, value: TRAVEL_MODE_MAPPING[modeStr] };
    }
    
    return { isValid: false, value: modeStr, error: '交通方式不正确' };
}

/**
 * 验证航班/船只号
 * @param {string} vesselName - 航班/船只号
 * @returns {Object} {isValid: boolean, value: string, error?: string}
 */
export function validateVesselName(vesselName) {
    if (!vesselName || typeof vesselName !== 'string') {
        return { isValid: false, value: '', error: '航班/船只号不能为空' };
    }
    
    const cleanVessel = vesselName.trim().toUpperCase();
    
    if (cleanVessel.length < 2 || cleanVessel.length > 20) {
        return { isValid: false, value: cleanVessel, error: '航班/船只号长度不正确' };
    }
    
    // 检查是否包含有效字符（字母、数字、连字符）
    const vesselRegex = /^[A-Z0-9\-]+$/;
    if (!vesselRegex.test(cleanVessel)) {
        return { isValid: false, value: cleanVessel, error: '航班/船只号格式不正确' };
    }
    
    return { isValid: true, value: cleanVessel };
}

/**
 * 验证邮政编码
 * @param {string|number} postcode - 邮政编码
 * @returns {Object} {isValid: boolean, value: string, error?: string}
 */
export function validatePostcode(postcode) {
    if (!postcode) {
        return { isValid: false, value: '', error: '邮政编码不能为空' };
    }
    
    const postcodeStr = String(postcode).replace(/[^\d]/g, '');
    
    if (postcodeStr.length !== 5) {
        return { isValid: false, value: postcodeStr, error: '马来西亚邮政编码应为5位数字' };
    }
    
    return { isValid: true, value: postcodeStr };
}

/**
 * 验证马来西亚州属代码
 * @param {string} stateCode - 州属代码
 * @returns {Object} {isValid: boolean, value: string, error?: string}
 */
export function validateStateCode(stateCode) {
    if (!stateCode) {
        return { isValid: false, value: '', error: '州属不能为空' };
    }
    
    const cleanCode = String(stateCode).padStart(2, '0');
    
    if (!MALAYSIA_STATES[cleanCode]) {
        return { isValid: false, value: cleanCode, error: '州属代码不正确' };
    }
    
    return { isValid: true, value: cleanCode };
}

/**
 * 批量验证表单数据
 * @param {Object} data - 要验证的数据
 * @returns {Object} {isValid: boolean, errors: string[], validatedData: Object}
 */
export function validateFormData(data) {
    console.log('🔍 开始批量验证表单数据...');
    
    const errors = [];
    const validatedData = {};
    
    // 验证各个字段
    const validations = [
        { key: 'name', validator: validateName, required: true },
        { key: 'passNo', validator: validatePassportNo, required: true },
        { key: 'dob', validator: (val) => validateDate(val, '出生日期'), required: true },
        { key: 'nationality', validator: validateNationality, required: true },
        { key: 'sex', validator: validateGender, required: true },
        { key: 'passExpDte', validator: (val) => validateDate(val, '护照有效期'), required: false },
        { key: 'email', validator: validateEmail, required: true },
        { key: 'region', validator: validatePhoneRegion, required: true },
        { key: 'mobile', validator: validateMobile, required: true },
        { key: 'arrDt', validator: (val) => validateDate(val, '到达日期'), required: true },
        { key: 'depDt', validator: (val) => validateDate(val, '离开日期'), required: true },
        { key: 'vesselNm', validator: validateVesselName, required: true },
        { key: 'trvlMode', validator: validateTravelMode, required: false },
        { key: 'accommodationPostcode', validator: validatePostcode, required: false }
    ];
    
    validations.forEach(({ key, validator, required }) => {
        const value = data[key];
        
        if (required && (!value || value.toString().trim() === '')) {
            errors.push(`${key}是必填字段`);
            return;
        }
        
        if (value && value.toString().trim() !== '') {
            const result = validator(value);
            if (result.isValid) {
                validatedData[key] = result.value;
            } else {
                errors.push(`${key}: ${result.error}`);
            }
        } else {
            validatedData[key] = value; // 保持原值
        }
    });
    
    // 复制其他字段
    Object.keys(data).forEach(key => {
        if (!validatedData.hasOwnProperty(key)) {
            validatedData[key] = data[key];
        }
    });
    
    // 确认邮箱与邮箱相同
    if (validatedData.email) {
        validatedData.confirmEmail = validatedData.email;
    }
    
    const isValid = errors.length === 0;
    
    if (isValid) {
        console.log('✅ 表单数据验证通过');
    } else {
        console.log('❌ 表单数据验证失败:', errors);
    }
    
    return { isValid, errors, validatedData };
}
