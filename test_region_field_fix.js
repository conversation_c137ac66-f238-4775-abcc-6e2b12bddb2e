// 测试电话区号字段修复
// 验证脚本是否正确使用 'region' 字段ID

console.log('🧪 测试电话区号字段修复');

// 模拟测试数据
const testData = {
    name: "张三",
    passNo: "E12345678",
    dob: "01/01/1990",
    nationality: "MYS",  // 马来西亚
    sex: "1",
    passExpDte: "01/01/2030",
    email: "<EMAIL>",
    confirmEmail: "<EMAIL>",
    region: "60",  // 马来西亚区号
    mobile: "123456789",
    arrDt: "01/12/2024",
    depDt: "05/12/2024",
    vesselNm: "MH123",
    trvlMode: "1"
};

// 测试字段ID映射
function testFieldMapping() {
    console.log('📋 测试字段ID映射:');
    
    // 检查实际网站字段是否存在
    const regionField = document.getElementById('region');
    const phoneRegionField = document.getElementById('phoneRegion');
    
    console.log('✅ 实际网站字段 #region 存在:', !!regionField);
    console.log('❌ 脚本字段 #phoneRegion 存在:', !!phoneRegionField);
    
    if (regionField) {
        console.log('🎯 找到正确的电话区号字段:', regionField.tagName, regionField.id);
        console.log('📊 当前选项数量:', regionField.options.length);
        
        // 测试设置马来西亚区号
        regionField.value = '60';
        console.log('🔧 设置马来西亚区号 (60)');
        console.log('✅ 当前值:', regionField.value);
        console.log('✅ 显示文本:', regionField.options[regionField.selectedIndex].text);
        
        // 触发change事件
        regionField.dispatchEvent(new Event('change'));
        console.log('🔄 已触发change事件');
    } else {
        console.error('❌ 未找到电话区号字段 (#region)');
    }
}

// 测试字段独立性
function testFieldIndependence() {
    console.log('\n🔒 测试字段独立性:');

    console.log('✅ 国籍和电话区号字段应保持独立');
    console.log('✅ 选择国籍时不应自动更改电话区号');
    console.log('✅ 用户可以独立设置任意国籍和区号组合');

    // 测试独立设置的示例
    const testCases = [
        { nationality: 'MYS', region: '60', desc: '马来西亚国籍 + 马来西亚区号' },
        { nationality: 'CHN', region: '60', desc: '中国国籍 + 马来西亚区号' },
        { nationality: 'MYS', region: '86', desc: '马来西亚国籍 + 中国区号' },
        { nationality: 'SGP', region: '1', desc: '新加坡国籍 + 美国区号' }
    ];

    testCases.forEach(testCase => {
        console.log(`🧪 ${testCase.desc}: ${testCase.nationality} + ${testCase.region}`);
    });

    console.log('💡 所有组合都应该被允许，不存在自动同步限制');
}

// 测试独立字段设置流程
function testIndependentFieldSetting() {
    console.log('\n🔧 测试独立字段设置流程:');

    // 测试场景1：设置不同国籍和区号的组合
    console.log('📋 测试场景1: 中国国籍 + 马来西亚区号');

    // 1. 设置国籍为中国
    const nationalityField = document.getElementById('nationality');
    if (nationalityField) {
        nationalityField.value = 'CHN';
        console.log('✅ 设置国籍为中国 (CHN)');
        nationalityField.dispatchEvent(new Event('change'));
    }

    // 2. 独立设置电话区号为马来西亚
    const regionField = document.getElementById('region');
    if (regionField) {
        regionField.value = '60';
        console.log('✅ 独立设置电话区号为马来西亚 (60)');
        regionField.dispatchEvent(new Event('change'));
    }

    // 3. 验证字段独立性
    setTimeout(() => {
        console.log('\n📊 字段独立性验证结果:');
        if (nationalityField) {
            console.log('🏳️ 国籍字段值:', nationalityField.value);
            console.log('🏳️ 国籍显示文本:', nationalityField.options[nationalityField.selectedIndex].text);
        }
        if (regionField) {
            console.log('📞 区号字段值:', regionField.value);
            console.log('📞 区号显示文本:', regionField.options[regionField.selectedIndex].text);
        }

        // 验证字段独立性：国籍和区号应该可以是不同国家的
        const isIndependent = nationalityField?.value === 'CHN' && regionField?.value === '60';
        console.log(isIndependent ? '✅ 字段独立性验证成功 - 可以设置不同国家的国籍和区号!' : '❌ 字段独立性验证失败');

        if (isIndependent) {
            console.log('🎉 确认：用户可以选择中国国籍但使用马来西亚电话区号');
        }
    }, 1000);
}

// 运行所有测试
function runAllTests() {
    console.log('🚀 开始运行电话区号字段独立性测试\n');

    testFieldMapping();
    testFieldIndependence();
    testIndependentFieldSetting();

    console.log('\n✅ 所有测试已完成');
    console.log('🔒 字段独立性已确认 - 国籍和电话区号可以独立设置');
}

// 等待页面加载完成后运行测试
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runAllTests);
} else {
    runAllTests();
}
