# MDAC智能填充助手 - 安装和使用指南

## 📦 安装步骤

### 1. 准备图标文件
在 `icons/` 文件夹中添加以下图标文件：
- `icon16.png` (16x16像素)
- `icon32.png` (32x32像素)  
- `icon48.png` (48x48像素)
- `icon128.png` (128x128像素)

**临时解决方案**：如果没有专门的图标，可以：
1. 使用在线图标生成器创建简单的"MDAC"文字图标
2. 或者从网上下载通用的表单/文档图标
3. 确保图标背景透明，颜色搭配合理

### 2. 安装Chrome扩展

1. **打开Chrome扩展管理页面**
   - 在Chrome地址栏输入：`chrome://extensions/`
   - 或者：菜单 → 更多工具 → 扩展程序

2. **启用开发者模式**
   - 在页面右上角开启"开发者模式"开关

3. **加载扩展**
   - 点击"加载已解压的扩展程序"按钮
   - 选择 `mdac-chrome-extension` 文件夹
   - 点击"选择文件夹"

4. **验证安装**
   - 扩展应该出现在扩展列表中
   - 浏览器工具栏应该显示扩展图标

## 🚀 使用方法

### 基本使用流程

1. **打开MDAC网站**
   - 访问：https://imigresen-online.imi.gov.my/mdac/main?registerMain
   - 等待页面完全加载

2. **启动扩展**
   - 点击浏览器工具栏中的MDAC扩展图标
   - 扩展popup窗口将打开

3. **检查页面状态**
   - 扩展会自动检测当前是否为MDAC页面
   - 绿色状态表示检测成功，红色表示需要先打开MDAC页面

4. **输入旅客信息**
   - 在"旅客信息输入"区域输入信息
   - 支持自然语言输入（推荐）
   - 也可以点击"个人信息"区域手动填写

5. **AI智能解析**（推荐）
   - 输入超过10个字符后，系统会在1秒后自动解析
   - 或者点击"⚡ 立即解析"按钮手动触发
   - AI会自动填充识别到的字段

6. **执行自动填充**
   - 点击"🎯 开始智能填充"按钮
   - 扩展会自动在MDAC页面填充表单
   - 观察状态提示和进度条

7. **检查和提交**
   - 返回MDAC页面检查填充结果
   - 手动调整任何需要修改的字段
   - 完成后提交表单

### 支持的输入格式

#### 自然语言格式（推荐）
```
李明，中国护照G12345678，1990年1月1日出生，男性，护照2026年1月1日到期。
邮箱：<EMAIL>，手机：+60123456789
计划2025年8月1日到达马来西亚，8月7日离开
乘坐MH123航班，住宿地址：吉隆坡市中心酒店，邮编50000
```

#### 结构化数据格式
```
name: LI MING
passport: G12345678  
birth: 1990-01-01
nationality: CHN
gender: 男
email: <EMAIL>
phone: +60123456789
arrival: 2025-08-01
departure: 2025-08-07
flight: MH123
address: Hotel KL City Center
postcode: 50000
```

## 🔧 功能特性

### ✅ 已实现功能
- 🤖 AI智能解析旅客信息
- 📝 一键自动填充MDAC表单
- 🔄 级联下拉菜单处理（州属→城市）
- 💾 常用信息持久化存储（邮箱、电话）
- ⚡ 实时执行状态反馈
- 🎯 页面自动检测
- 📋 示例数据快速填入
- 🗑️ 表单快速清空

### 🎯 智能特性
- **自动解析**：输入文本1秒后自动触发AI解析
- **选择性填充**：只填充AI识别到的字段，保留现有内容
- **字段映射**：自动处理脚本字段与网站字段的差异
- **数据验证**：填充前验证数据格式和完整性
- **错误处理**：详细的错误提示和重试机制

## 🐛 故障排除

### 常见问题

1. **扩展图标不显示**
   - 确保已正确安装图标文件
   - 刷新扩展管理页面
   - 重新加载扩展

2. **页面检测失败**
   - 确保已打开正确的MDAC网站
   - 刷新MDAC页面
   - 检查网络连接

3. **AI解析失败**
   - 检查输入文本是否足够详细
   - 确保网络连接正常
   - 尝试使用示例数据测试

4. **表单填充不完整**
   - 检查MDAC页面是否完全加载
   - 某些字段可能需要手动调整
   - 查看浏览器控制台的详细日志

5. **城市选择失败**
   - 州属选择后需要等待3秒让城市列表加载
   - 地址信息要包含具体的城市关键词
   - 可以手动选择城市

### 调试方法

1. **查看控制台日志**
   - 按F12打开开发者工具
   - 查看Console标签页的日志信息
   - 寻找错误信息和执行状态

2. **检查扩展状态**
   - 在扩展管理页面查看扩展是否正常运行
   - 查看是否有错误提示

3. **重新加载扩展**
   - 在扩展管理页面点击刷新按钮
   - 或者移除后重新安装

## 📞 技术支持

如果遇到问题，请：
1. 查看浏览器控制台的错误信息
2. 确认MDAC网站页面结构是否有变化
3. 检查网络连接和API访问权限
4. 尝试使用示例数据进行测试

## 🔄 更新说明

### 版本 1.0.0
- ✅ 基础自动填充功能
- ✅ AI智能解析
- ✅ 页面检测和状态反馈
- ✅ 持久化数据存储
- ✅ 级联下拉菜单处理

---

**注意**：本扩展仅供内部使用，请确保在使用前已获得相关授权。
