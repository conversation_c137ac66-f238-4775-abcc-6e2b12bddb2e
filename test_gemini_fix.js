// 测试修复后的Gemini解析功能
console.log('🧪 测试修复后的Gemini解析功能...');

// 模拟你的输入数据
const testInput = `1    陈泰宇    CHEN TAIYU    男    2014年2月7日    2029年7月17日    EM8990705    2024年7月18日    2`;

// 模拟修复后的prompt结构
const prompt = `
你是一个专业的MDAC表单数据提取助手。请从以下旅客信息中提取数据，并返回严格的JSON格式。

解析规则：
- 如果有中文姓名和英文姓名，优先使用英文姓名
- 性别："男"=1, "女"=2
- 日期格式：将年月日转换为DD/MM/YYYY格式
- 如果信息不完整，对应字段返回null
- 根据姓名特征推断国籍（如中文姓名推断为CHN）
- 根据国籍设置对应的电话区号

旅客信息：
${testInput}

重要提示：
1. 请仔细解析上述实际旅客信息，不要使用示例数据
2. 只返回JSON格式的数据，不要包含任何其他文字或解释
3. 根据实际旅客信息填写字段值，如果某个字段无法从提供的信息中提取，请返回null
4. 确保所有提取的数据都来自于实际的旅客信息，而不是示例或模板数据

JSON格式示例：
{
    "name": null,
    "passNo": null,
    "dob": null,
    "nationality": null,
    "sex": null,
    "passExpDte": null,
    "email": null,
    "confirmEmail": null,
    "region": null,
    "mobile": null,
    "arrDt": null,
    "depDt": null,
    "vesselNm": null,
    "trvlMode": null,
    "embark": null,
    "accommodationStay": null,
    "accommodationAddress1": null,
    "accommodationAddress2": null,
    "accommodationState": null,
    "accommodationPostcode": null
}
`;

console.log('📝 修复后的Prompt:', prompt);

// 期望的解析结果
const expectedResult = {
    name: "CHEN TAIYU",
    passNo: "EM8990705",
    dob: "07/02/2014",
    nationality: "CHN",
    sex: "1",
    passExpDte: "17/07/2029",
    email: null,
    confirmEmail: null,
    region: "86",  // 将由validateAndCleanData自动设置
    mobile: null,
    arrDt: "18/07/2024",
    depDt: null,
    vesselNm: null,
    trvlMode: null,
    embark: "CHN",  // 将由validateAndCleanData自动设置
    accommodationStay: null,
    accommodationAddress1: null,
    accommodationAddress2: null,
    accommodationState: null,
    accommodationPostcode: null
};

console.log('🎯 期望的解析结果:', expectedResult);

// 测试修复后的数据验证和清理逻辑
function testValidateAndCleanData(data) {
    console.log('\n🧹 测试数据验证和清理逻辑...');
    
    const cleaned = { ...data };
    
    console.log('🔍 输入数据:', data);
    
    // 自动填充confirmEmail（仅当email有值时）
    if (cleaned.email && cleaned.email !== null && !cleaned.confirmEmail) {
        cleaned.confirmEmail = cleaned.email;
        console.log('🔧 自动填充confirmEmail:', cleaned.email);
    }
    
    // 自动填充embark（仅当nationality有值时）
    if (cleaned.nationality && cleaned.nationality !== null && !cleaned.embark) {
        cleaned.embark = cleaned.nationality;
        console.log('🔧 自动填充embark:', cleaned.nationality);
    }
    
    // 根据国籍自动设置区号
    if ((!cleaned.region || cleaned.region === null) && cleaned.nationality && cleaned.nationality !== null) {
        const nationalityToRegion = {
            'CHN': '86', 'USA': '1', 'GBR': '44', 'SGP': '65', 'JPN': '81', 'MYS': '60'
        };
        cleaned.region = nationalityToRegion[cleaned.nationality] || '86';
        console.log('🔧 根据国籍自动设置区号:', cleaned.nationality, '→', cleaned.region);
    }
    
    console.log('✅ 清理后的数据:', cleaned);
    return cleaned;
}

// 测试选择性填充逻辑
function testSelectiveFilling(data) {
    console.log('\n🎯 测试选择性填充逻辑...');
    
    const fieldMapping = {
        'name': 'passengerName',
        'passNo': 'passportNo',
        'dob': 'birthDate',
        'nationality': 'nationality',
        'sex': 'gender',
        'passExpDte': 'passportExpiry',
        'email': 'email',
        'confirmEmail': 'confirmEmail',
        'region': 'phoneRegion',
        'mobile': 'phoneNumber',
        'arrDt': 'arrivalDate',
        'depDt': 'departureDate',
        'vesselNm': 'flightNo',
        'trvlMode': 'travelMode',
        'embark': 'embark',
        'accommodationStay': 'accommodationType',
        'accommodationAddress1': 'address1',
        'accommodationAddress2': 'address2',
        'accommodationState': 'state',
        'accommodationPostcode': 'postcode'
    };
    
    let updateCount = 0;
    let skipCount = 0;
    
    Object.keys(fieldMapping).forEach(dataKey => {
        const formFieldId = fieldMapping[dataKey];
        const value = data[dataKey];
        
        if (value === null || value === undefined || value === '') {
            console.log(`⏸️ 跳过空字段 ${dataKey} → ${formFieldId}`);
            skipCount++;
        } else {
            console.log(`✅ 将更新字段 ${dataKey} → ${formFieldId}: "${value}"`);
            updateCount++;
        }
    });
    
    console.log(`📊 预期结果: ${updateCount} 个字段将被更新, ${skipCount} 个字段将被跳过`);
}

// 执行测试
console.log('\n🚀 开始测试...');
const cleanedData = testValidateAndCleanData(expectedResult);
testSelectiveFilling(cleanedData);

console.log('\n📋 修复总结:');
console.log('✅ 1. 移除了prompt中的示例数据干扰');
console.log('✅ 2. 增强了数据解析指令的明确性');
console.log('✅ 3. 修复了validateAndCleanData函数的过度严格验证');
console.log('✅ 4. 增加了详细的调试日志');
console.log('✅ 5. 改进了选择性填充逻辑');

console.log('\n💡 现在AI应该能正确解析你的数据了！');