// 测试脚本：验证选择性更新和州属-城市关联功能
// 使用方法：在MDAC页面控制台中运行此脚本

console.log('🧪 开始测试选择性更新功能...');

// 模拟Gemini返回的部分数据（有些字段为空）
const testData = {
    name: "TEST USER",
    passNo: "E1234567",
    dob: null,  // 空字段
    nationality: "MYS",
    sex: null,  // 空字段
    passExpDte: "01/01/2030",
    email: null,  // 空字段
    confirmEmail: null,  // 空字段
    region: "60",  // 马来西亚
    mobile: null,  // 空字段
    arrDt: "20/01/2025",
    depDt: null,  // 空字段
    vesselNm: "MH123",
    trvlMode: null,  // 空字段
    embark: null,  // 空字段
    accommodationStay: "1",
    accommodationAddress1: "Legoland Malaysia",
    accommodationAddress2: null,  // 空字段
    accommodationState: "01",  // 柔佛州
    accommodationPostcode: "79100"
};

// 测试选择性字段更新函数
function testFillFieldSelectively(selector, value, isSelect = false, fieldName = '') {
    console.log(`🔍 测试字段: ${fieldName} (${selector})`);
    
    // 跳过空值、null值和undefined值
    if (value === null || value === undefined || value === '') {
        console.log(`⏸️ 跳过空字段 ${fieldName} (${selector})`);
        return false;
    }
    
    const element = document.querySelector(selector);
    if (!element) {
        console.log(`❌ 元素未找到: ${selector}`);
        return false;
    }
    
    // 记录原始值
    const originalValue = element.value;
    console.log(`📝 原始值: "${originalValue}" → 新值: "${value}"`);
    
    // 更新字段
    if (isSelect && element.tagName === 'SELECT') {
        element.value = value;
        const targetOption = element.querySelector(`option[value="${value}"]`);
        if (targetOption) {
            targetOption.setAttribute('selected', '');
            console.log(`✅ 下拉菜单更新成功: ${fieldName} = ${value}`);
        } else {
            console.log(`❌ 下拉菜单选项未找到: ${value}`);
            return false;
        }
    } else {
        element.value = value;
        console.log(`✅ 字段更新成功: ${fieldName} = ${value}`);
    }
    
    // 触发事件
    ['input', 'change', 'blur'].forEach(eventType => {
        const event = new Event(eventType, { bubbles: true });
        element.dispatchEvent(event);
    });
    
    return true;
}

// 执行测试
console.log('🎯 开始测试个人信息字段...');
testFillFieldSelectively('#name', testData.name, false, '姓名');
testFillFieldSelectively('#passNo', testData.passNo, false, '护照号');
testFillFieldSelectively('#dob', testData.dob, false, '出生日期');
testFillFieldSelectively('#nationality', testData.nationality, true, '国籍');
testFillFieldSelectively('#sex', testData.sex, true, '性别');
testFillFieldSelectively('#passExpDte', testData.passExpDte, false, '护照到期日');
testFillFieldSelectively('#email', testData.email, false, '邮箱');
testFillFieldSelectively('#confirmEmail', testData.confirmEmail, false, '确认邮箱');
testFillFieldSelectively('#region', testData.region, true, '地区代码');
testFillFieldSelectively('#mobile', testData.mobile, false, '手机号');

console.log('🎯 开始测试旅行信息字段...');
testFillFieldSelectively('#arrDt', testData.arrDt, false, '到达日期');
testFillFieldSelectively('#depDt', testData.depDt, false, '离开日期');
testFillFieldSelectively('#vesselNm', testData.vesselNm, false, '航班号');
testFillFieldSelectively('#trvlMode', testData.trvlMode, true, '交通方式');
testFillFieldSelectively('#embark', testData.embark, true, '出发地');

console.log('🎯 开始测试住宿信息字段...');
testFillFieldSelectively('#accommodationStay', testData.accommodationStay, true, '住宿类型');
testFillFieldSelectively('#accommodationAddress1', testData.accommodationAddress1, false, '住宿地址1');
testFillFieldSelectively('#accommodationAddress2', testData.accommodationAddress2, false, '住宿地址2');
testFillFieldSelectively('#accommodationPostcode', testData.accommodationPostcode, false, '邮政编码');

// 测试州属选择
console.log('🎯 开始测试州属选择...');
const stateElement = document.querySelector('#accommodationState');
if (stateElement) {
    const currentState = stateElement.value;
    console.log(`🏛️ 当前州属: "${currentState}"`);
    
    if (testData.accommodationState && currentState !== testData.accommodationState) {
        console.log(`🔄 更新州属: "${currentState}" → "${testData.accommodationState}"`);
        testFillFieldSelectively('#accommodationState', testData.accommodationState, true, '州属');
        
        // 等待城市列表加载
        setTimeout(() => {
            const citySelect = document.querySelector('#accommodationCity');
            if (citySelect && citySelect.options.length > 1) {
                console.log(`🏙️ 城市列表已加载，共 ${citySelect.options.length - 1} 个选项`);
                
                // 尝试智能选择城市
                const keywords = ['legoland', 'johor bahru', 'jb'];
                for (let keyword of keywords) {
                    for (let option of citySelect.options) {
                        if (option.text.toLowerCase().includes(keyword)) {
                            option.selected = true;
                            citySelect.value = option.value;
                            console.log(`✅ 城市选择成功: ${option.text}`);
                            return;
                        }
                    }
                }
                console.log('⚠️ 未找到匹配的城市，需要手动选择');
            } else {
                console.log('❌ 城市列表加载失败或为空');
            }
        }, 2000);
    } else {
        console.log('⏸️ 州属无需更新');
    }
} else {
    console.log('❌ 州属元素未找到');
}

console.log('🎉 测试完成！');
console.log('💡 提示：空字段已被跳过，现有表单内容得到保留');